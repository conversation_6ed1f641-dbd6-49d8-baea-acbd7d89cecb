// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get done => 'Done';

  @override
  String get loading => 'Loading ...';

  @override
  String get messageHasBeenDeleted => 'Message has been deleted';

  @override
  String get mute => 'Mute';

  @override
  String get cancel => 'Cancel';

  @override
  String get typing => 'Typing...';

  @override
  String get ok => 'OK';

  @override
  String get recording => 'Recording...';

  @override
  String get connecting => 'Connecting...';

  @override
  String get deleteYouCopy => 'Delete your copy';

  @override
  String get unMute => 'Un mute';

  @override
  String get delete => 'Delete';

  @override
  String get report => 'Report';

  @override
  String get leaveGroup => 'Leave group';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'Are you sure to permit your copy? This action can\'t undo';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'Are you sure to leave this group? This action can\'t undo';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Leave group and delete your message copy';

  @override
  String get vMessageInfoTrans => 'Message info';

  @override
  String get updateTitleTo => 'Update title to';

  @override
  String get updateImage => 'Update image';

  @override
  String get joinedBy => 'Joined by';

  @override
  String get promotedToAdminBy => 'Promoted to admin by';

  @override
  String get dismissedToMemberBy => 'Dismissed to member by';

  @override
  String get leftTheGroup => 'Left the group';

  @override
  String get you => 'You';

  @override
  String get kickedBy => 'Kicked by';

  @override
  String get groupCreatedBy => 'Group created by';

  @override
  String get addedYouToNewBroadcast => 'Added you to new broadcast';

  @override
  String get download => 'Download';

  @override
  String get copy => 'Copy';

  @override
  String get info => 'Info';

  @override
  String get share => 'Share';

  @override
  String get forward => 'Forward';

  @override
  String get reply => 'Reply';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Delete from all';

  @override
  String get deleteFromMe => 'Delete from me';

  @override
  String get downloading => 'Downloading...';

  @override
  String get fileHasBeenSavedTo => 'File has been saved to';

  @override
  String get online => 'Online';

  @override
  String get youDontHaveAccess => 'You don\'t have access';

  @override
  String get replyToYourSelf => 'Reply to your self';

  @override
  String get repliedToYourSelf => 'Replied to your self';

  @override
  String get audioCall => 'Audio call';

  @override
  String get ring => 'Ring';

  @override
  String get canceled => 'Canceled';

  @override
  String get timeout => 'Timeout';

  @override
  String get rejected => 'Rejected';

  @override
  String get finished => 'Finished';

  @override
  String get inCall => 'In call';

  @override
  String get sessionEnd => 'Session end';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get today => 'Today';

  @override
  String get textFieldHint => 'Type a message ...';

  @override
  String get files => 'Files';

  @override
  String get location => 'Location';

  @override
  String get shareMediaAndLocation => 'Share media and location';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'There is video size bigger than allowed size';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'There is file has size bigger than allowed size';

  @override
  String get makeCall => 'Make call';

  @override
  String get areYouWantToMakeVideoCall => 'Are you want to make video call?';

  @override
  String get areYouWantToMakeVoiceCall => 'Are you want to make voice call?';

  @override
  String get vMessagesInfoTrans => 'Messages info';

  @override
  String get star => 'Star';

  @override
  String get minutes => 'Minutes';

  @override
  String get sendMessage => 'Send message';

  @override
  String get deleteUser => 'Delete user';

  @override
  String get actions => 'Actions';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'You are about to delete this user from your list';

  @override
  String get updateBroadcastTitle => 'Update broadcast title';

  @override
  String get usersAddedSuccessfully => 'Users added successfully';

  @override
  String get broadcastSettings => 'Broadcast settings';

  @override
  String get addParticipants => 'Add Participants';

  @override
  String get broadcastParticipants => 'Broadcast Participants';

  @override
  String get updateGroupDescription => 'Update group description';

  @override
  String get updateGroupTitle => 'Update group title';

  @override
  String get groupSettings => 'Group settings';

  @override
  String get description => 'Description';

  @override
  String get muteNotifications => 'Mute notifications';

  @override
  String get groupParticipants => 'Group Participants';

  @override
  String get blockUser => 'Block user';

  @override
  String get areYouSureToBlock => 'Are you sure to block';

  @override
  String get userPage => 'User page';

  @override
  String get starMessage => 'Star message';

  @override
  String get showMedia => 'Show media';

  @override
  String get reportUser => 'Report user';

  @override
  String get groupName => 'group name';

  @override
  String get changeSubject => 'Change subject';

  @override
  String get titleIsRequired => 'Title is required';

  @override
  String get createBroadcast => 'Create Broadcast';

  @override
  String get broadcastName => 'Broadcast name';

  @override
  String get createGroup => 'Create Group';

  @override
  String get forgetPassword => 'Forget Password?';

  @override
  String get globalSearch => 'Global Search';

  @override
  String get dismissesToMember => 'Dismisses to member';

  @override
  String get setToAdmin => 'Set to admin';

  @override
  String get kickMember => 'Kick member';

  @override
  String get youAreAboutToDismissesToMember =>
      'You are about to dismisses to member';

  @override
  String get youAreAboutToKick => 'You are about to kick';

  @override
  String get groupMembers => 'Group Members';

  @override
  String get tapForPhoto => 'Tap for photo';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'We high recommend to download this update';

  @override
  String get newGroup => 'New group';

  @override
  String get newBroadcast => 'New broadcast';

  @override
  String get starredMessage => 'Starred message';

  @override
  String get settings => 'Settings';

  @override
  String get chats => 'CHATS';

  @override
  String get recentUpdates => 'Recent updates';

  @override
  String get startChat => 'Start chat';

  @override
  String get newUpdateIsAvailable => 'New update is available';

  @override
  String get emailNotValid => 'Email not valid';

  @override
  String get passwordMustHaveValue => 'Password must have value';

  @override
  String get error => 'Error';

  @override
  String get password => 'Password';

  @override
  String get login => 'Login';

  @override
  String get needNewAccount => 'Need new account?';

  @override
  String get register => 'Register';

  @override
  String get nameMustHaveValue => 'Name must have value';

  @override
  String get passwordNotMatch => 'Password not match';

  @override
  String get name => 'Name';

  @override
  String get email => 'Email';

  @override
  String get confirmPassword => 'Confirm password';

  @override
  String get alreadyHaveAnAccount => 'Already have an account?';

  @override
  String get logOut => 'Log out';

  @override
  String get back => 'Back';

  @override
  String get sendCodeToMyEmail => 'Send code to my email';

  @override
  String get invalidLoginData => 'Invalid login data';

  @override
  String get userEmailNotFound => 'User email not found';

  @override
  String get yourAccountBlocked => 'Your account has been baned';

  @override
  String get yourAccountDeleted => 'Your account has been deleted';

  @override
  String get userAlreadyRegister => 'User already register';

  @override
  String get codeHasBeenExpired => 'Code has been expired';

  @override
  String get invalidCode => 'Invalid code';

  @override
  String get whileAuthCanFindYou => 'While authentication cannot find you';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'User register status not accepted yet';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'Device has been logout from all devices';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'User device session end device deleted';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'No code has been send to you to verify your email';

  @override
  String get roomAlreadyInCall => 'Room already in call';

  @override
  String get peerUserInCallNow => 'User in call now';

  @override
  String get callNotAllowed => 'Call not allowed';

  @override
  String get peerUserDeviceOffline => 'Peer user device offline';

  @override
  String get emailMustBeValid => 'Email must be valid';

  @override
  String get wait2MinutesToSendMail => 'Wait 2 minutes to send mail';

  @override
  String get codeMustEqualToSixNumbers => 'Code must equal to six numbers';

  @override
  String get newPasswordMustHaveValue => 'New password must have value';

  @override
  String get confirmPasswordMustHaveValue => 'Confirm password must have value';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Congregations your account has been accepted';

  @override
  String get yourAccountIsUnderReview => 'Your account is under review';

  @override
  String get waitingList => 'Waiting List';

  @override
  String get welcome => 'Welcome';

  @override
  String get retry => 'Retry';

  @override
  String get deleteMember => 'Delete member';

  @override
  String get profile => 'Profile';

  @override
  String get broadcastInfo => 'Broadcast info';

  @override
  String get updateTitle => 'Update title';

  @override
  String get members => 'Members';

  @override
  String get addMembers => 'Add Members';

  @override
  String get success => 'Success';

  @override
  String get media => 'Media';

  @override
  String get docs => 'Docs';

  @override
  String get links => 'Links';

  @override
  String get soon => 'Soon';

  @override
  String get unStar => 'Un star';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Update group description will update all group members';

  @override
  String get updateNickname => 'Update nickname';

  @override
  String get groupInfo => 'Group info';

  @override
  String get youNotParticipantInThisGroup =>
      'You not participant in this group';

  @override
  String get search => 'Search';

  @override
  String get mediaLinksAndDocs => 'Media, Links, and Docs';

  @override
  String get starredMessages => 'Starred Messages';

  @override
  String get nickname => 'Nickname';

  @override
  String get none => 'None';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get exitGroup => 'Exit Group';

  @override
  String get clickToAddGroupDescription => 'Click to add group description';

  @override
  String get unBlockUser => 'Un block user';

  @override
  String get areYouSureToUnBlock => 'Are you sure to un block';

  @override
  String get contactInfo => 'Contact info';

  @override
  String get audio => 'Audio';

  @override
  String get video => 'Video';

  @override
  String get hiIamUse => 'Hi iam using';

  @override
  String get on => 'On';

  @override
  String get off => 'Off';

  @override
  String get unBlock => 'Un Block';

  @override
  String get block => 'Block';

  @override
  String get chooseAtLestOneMember => 'Choose at lest one member';

  @override
  String get close => 'Close';

  @override
  String get next => 'Next';

  @override
  String get appMembers => 'App members';

  @override
  String get create => 'Create';

  @override
  String get upgradeToAdmin => 'Upgrade to admin';

  @override
  String get update => 'Update';

  @override
  String get deleteChat => 'Delete chat';

  @override
  String get clearChat => 'Clear chat';

  @override
  String get showHistory => 'Show history';

  @override
  String get groupIcon => 'Group icon';

  @override
  String get tapToSelectAnIcon => 'Tap to select an icon';

  @override
  String get groupDescription => 'Group description';

  @override
  String get more => 'More';

  @override
  String get messageInfo => 'Message info';

  @override
  String get successfullyDownloadedIn => 'Successfully downloaded in';

  @override
  String get delivered => 'Delivered';

  @override
  String get read => 'Read';

  @override
  String get orLoginWith => 'or login with';

  @override
  String get resetPassword => 'Reset password';

  @override
  String get otpCode => 'OTP Code';

  @override
  String get newPassword => 'New password';

  @override
  String get areYouSure => 'Are you sure?';

  @override
  String get broadcastMembers => 'Broadcast Members';

  @override
  String get phone => 'Phone';

  @override
  String get users => 'Users';

  @override
  String get calls => 'Calls';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Your are about to logout from this account';

  @override
  String get noUpdatesAvailableNow => 'No updates available now';

  @override
  String get dataPrivacy => 'Data privacy';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'All data has been backup you don\\\'t want need to manage save the data by your self! if you logout and login again you will see all chats same for web version';

  @override
  String get account => 'Account';

  @override
  String get linkedDevices => 'Linked Devices';

  @override
  String get storageAndData => 'Storage and Data';

  @override
  String get tellAFriend => 'Tell a friend';

  @override
  String get help => 'Help';

  @override
  String get blockedUsers => 'Blocked users';

  @override
  String get inAppAlerts => 'In app alerts';

  @override
  String get language => 'Language';

  @override
  String get adminNotification => 'Admin notification';

  @override
  String get checkForUpdates => 'Check for updates';

  @override
  String get linkByQrCode => 'Link By Qr Code';

  @override
  String get deviceStatus => 'Device status';

  @override
  String get desktopAndOtherDevices => 'Desktop, and other devices';

  @override
  String get linkADeviceSoon => 'Link a Device (Soon)';

  @override
  String get lastActiveFrom => 'Last active from';

  @override
  String get tapADeviceToEditOrLogOut => 'Tap a device to edit or log out.';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get supportChatSoon => 'Support chat (Soon)';

  @override
  String get updateYourName => 'Update your name';

  @override
  String get updateYourBio => 'Update your bio';

  @override
  String get edit => 'Edit';

  @override
  String get about => 'About';

  @override
  String get oldPassword => 'Old password';

  @override
  String get deleteMyAccount => 'Delete my account';

  @override
  String get passwordHasBeenChanged => 'Password has been changed';

  @override
  String get logoutFromAllDevices => 'Logout from all devices?';

  @override
  String get updateYourPassword => 'Update your password';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Enter your name and add an optional profile picture';

  @override
  String get privacyPolicy => 'Privacy policy';

  @override
  String get chat => 'Chat';

  @override
  String get send => 'Send';

  @override
  String get reportHasBeenSubmitted => 'Your report has been submitted';

  @override
  String get offline => 'Offline';

  @override
  String get harassmentOrBullyingDescription =>
      'Harassment or Bullying: This option allows users to report individuals who are targeting them or others with harassing messages, threats, or other forms of bullying.';

  @override
  String get spamOrScamDescription =>
      'Spam or Scam: This option would be for users to report accounts that are sending spam messages, unsolicited advertisements, or are attempting to scam others.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Are you sure to submit report about this user to the admin?';

  @override
  String get groupWith => 'Group with';

  @override
  String get inappropriateContentDescription =>
      'Inappropriate Content: Users can select this option to report any sexually explicit material, hate speech, or other content that violates community standards.';

  @override
  String get otherCategoryDescription =>
      'Other: This catch-all category can be used for violations that don\'t easily fit into the above categories. It might be helpful to include a text box for users to provide additional details.';

  @override
  String get explainWhatHappens => 'Explain here what happens';

  @override
  String get loginAgain => 'Login again!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Your session is ended please login again!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'You are about to block this user. You can\'t send him chats and can\'t add him to groups or broadcast!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'You are about to delete your account your account will not appears again in the users list';

  @override
  String get admin => 'Admin';

  @override
  String get member => 'Member';

  @override
  String get creator => 'Creator';

  @override
  String get currentDevice => 'Current device';

  @override
  String get visits => 'Visits';

  @override
  String get chooseRoom => 'Choose room';

  @override
  String get deleteThisDeviceDesc =>
      'Deleting this device means instantly logout this device';

  @override
  String get youAreAboutToUpgradeToAdmin => 'You are about to upgrade to admin';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Login now allowed. Please try again later.';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get notification => 'Notification';

  @override
  String get total => 'Total';

  @override
  String get blocked => 'Blocked';

  @override
  String get deleted => 'Deleted';

  @override
  String get accepted => 'Accepted';

  @override
  String get notAccepted => 'Not Accepted';

  @override
  String get web => 'Web';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Other';

  @override
  String get totalVisits => 'Total Visits';

  @override
  String get totalMessages => 'Total Messages';

  @override
  String get textMessages => 'Text Messages';

  @override
  String get imageMessages => 'Image Messages';

  @override
  String get videoMessages => 'Video Messages';

  @override
  String get voiceMessages => 'Voice Messages';

  @override
  String get fileMessages => 'File Messages';

  @override
  String get infoMessages => 'Info Messages';

  @override
  String get voiceCallMessages => 'Voice Call Messages';

  @override
  String get videoCallMessages => 'Video Call Messages';

  @override
  String get locationMessages => 'Location Messages';

  @override
  String get directChat => 'Direct Chat';

  @override
  String get group => 'Group';

  @override
  String get broadcast => 'Broadcast';

  @override
  String get messageCounter => 'Message Counter';

  @override
  String get roomCounter => 'Room Counter';

  @override
  String get countries => 'Countries';

  @override
  String get devices => 'Devices';

  @override
  String get notificationTitle => 'Notification Title';

  @override
  String get notificationDescription => 'Notification Description';

  @override
  String get notificationsPage => 'Notifications Page';

  @override
  String get updateFeedBackEmail => 'Update Feedback Email';

  @override
  String get setMaxMessageForwardAndShare =>
      'Set Max Message Forward and Share';

  @override
  String get setNewPrivacyPolicyUrl => 'Set New Privacy Policy URL';

  @override
  String get forgetPasswordExpireTime => 'Forget Password Expire Time';

  @override
  String get callTimeoutInSeconds => 'Call Timeout in Seconds';

  @override
  String get setMaxGroupMembers => 'Set Max Group Members';

  @override
  String get setMaxBroadcastMembers => 'Set Max Broadcast Members';

  @override
  String get allowCalls => 'Allow Calls';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'If this option is enabled, the video and voice call will be allowed.';

  @override
  String get allowAds => 'Allow Ads';

  @override
  String get allowMobileLogin => 'Allow Mobile Login';

  @override
  String get allowWebLogin => 'Allow Web Login';

  @override
  String get messages => 'Messages';

  @override
  String get appleStoreAppUrl => 'Apple Store App URL';

  @override
  String get googlePlayAppUrl => 'Google Play App URL';

  @override
  String get privacyUrl => 'Privacy URL';

  @override
  String get feedBackEmail => 'Feedback Email';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'If this option is disabled, sending chat files, images, videos, and location will be blocked.';

  @override
  String get allowSendMedia => 'Allow Send Media';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'If this option is disabled, creating chat broadcast will be blocked.';

  @override
  String get allowCreateBroadcast => 'Allow Create Broadcast';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'If this option is disabled, creating chat groups will be blocked.';

  @override
  String get allowCreateGroups => 'Allow Create Groups';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'If this option is disabled, the desktop login or register (Windows, Mac) will be blocked.';

  @override
  String get allowDesktopLogin => 'Allow Desktop Login';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'If this option is disabled, the web login or register will be blocked.';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'If this option is disabled, the mobile login or register will be blocked on Android and iOS only.';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'User Profile';

  @override
  String get userInfo => 'User Info';

  @override
  String get fullName => 'Full Name';

  @override
  String get bio => 'Bio';

  @override
  String get noBio => 'No Bio';

  @override
  String get verifiedAt => 'Verified At';

  @override
  String get country => 'Country';

  @override
  String get registerStatus => 'Register Status';

  @override
  String get registerMethod => 'Register Method';

  @override
  String get banTo => 'Ban To';

  @override
  String get deletedAt => 'Deleted At';

  @override
  String get createdAt => 'Created At';

  @override
  String get updatedAt => 'Updated At';

  @override
  String get reports => 'Reports';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Click to see all user devices details';

  @override
  String get allDeletedMessages => 'All Deleted Messages';

  @override
  String get voiceCallMessage => 'Voice Call Message';

  @override
  String get totalRooms => 'Total Rooms';

  @override
  String get directRooms => 'Direct Rooms';

  @override
  String get userAction => 'User Action';

  @override
  String get status => 'Status';

  @override
  String get joinedAt => 'Joined At';

  @override
  String get saveLogin => 'Save Login';

  @override
  String get passwordIsRequired => 'Password is required';

  @override
  String get verified => 'Verified';

  @override
  String get pending => 'Pending';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Description is required';

  @override
  String get seconds => 'Seconds';

  @override
  String get clickToSeeAllUserInformations =>
      'Click to see all user information';

  @override
  String get clickToSeeAllUserCountries => 'Click to see all user countries';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Click to see all user messages details';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Click to see all user rooms details';

  @override
  String get clickToSeeAllUserReports => 'Click to see all user reports';

  @override
  String get banAt => 'Ban at';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Now you login as read-only admin. All edits you make will not be applied due to this being a test version.';

  @override
  String get createStory => 'Create Story';

  @override
  String get writeACaption => 'Write a caption...';

  @override
  String get storyCreatedSuccessfully => 'Story Created Successfully';

  @override
  String get stories => 'Stories';

  @override
  String get clear => 'Clear';

  @override
  String get clearCallsConfirm => 'Clear calls confirm';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Choose how automatic download works';

  @override
  String get whenUsingMobileData => 'When using mobile data';

  @override
  String get whenUsingWifi => 'When using Wi-Fi';

  @override
  String get image => 'Image';

  @override
  String get myPrivacy => 'My Privacy';

  @override
  String get createTextStory => 'Create Text Story';

  @override
  String get createMediaStory => 'Create Media Story';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get recentUpdate => 'Recent update';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Add new story';

  @override
  String get updateYourProfile => 'Update your profile';

  @override
  String get configureYourAccountPrivacy => 'Configure your account privacy';

  @override
  String get youInPublicSearch => 'You in public search';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Your profile appears in public search and adding for groups';

  @override
  String get yourLastSeen => 'Your last seen';

  @override
  String get yourLastSeenInChats => 'Your last seen in chats';

  @override
  String get startNewChatWithYou => 'Start new chat with you';

  @override
  String get yourStory => 'Your story';

  @override
  String get forRequest => 'For request';

  @override
  String get public => 'Public';

  @override
  String get createYourStory => 'Create your story';

  @override
  String get shareYourStatus => 'Share your status';

  @override
  String get oneSeenMessage => 'One seen message';

  @override
  String get messageHasBeenViewed => 'Message has been viewed';

  @override
  String get clickToSee => 'Click to see';

  @override
  String get images => 'Images';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String areYouSureRemoveAccount(Object name) {
    return 'Are you sure you want to remove the account for $name?';
  }

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';
}
