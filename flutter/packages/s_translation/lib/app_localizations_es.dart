// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get done => 'Hecho';

  @override
  String get loading => 'Cargando ...';

  @override
  String get messageHasBeenDeleted => 'Se ha eliminado el mensaje';

  @override
  String get mute => 'Silenciar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get typing => 'Escribiendo ...';

  @override
  String get ok => 'DE ACUERDO';

  @override
  String get recording => 'Grabación ...';

  @override
  String get connecting => 'Conectando ...';

  @override
  String get deleteYouCopy => 'Elimina tu copia';

  @override
  String get unMute => 'Un mudo';

  @override
  String get delete => 'Borrar';

  @override
  String get report => 'Aplazamiento';

  @override
  String get leaveGroup => 'Dejar el grupo';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      '¿Estás seguro de permitir tu copia?Esta acción no puede deshacer';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      '¿Estás seguro de dejar este grupo?Esta acción no puede deshacer';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Deja el grupo y elimina la copia de tu mensaje';

  @override
  String get vMessageInfoTrans => 'Mensaje de información';

  @override
  String get updateTitleTo => 'Actualizar el título de';

  @override
  String get updateImage => 'Imagen de actualización';

  @override
  String get joinedBy => 'Unido por';

  @override
  String get promotedToAdminBy => 'Ascendido a administrador por';

  @override
  String get dismissedToMemberBy => 'Disminizado al miembro por';

  @override
  String get leftTheGroup => 'Dejó el grupo';

  @override
  String get you => 'Tú';

  @override
  String get kickedBy => 'Pateado por';

  @override
  String get groupCreatedBy => 'Grupo creado por';

  @override
  String get addedYouToNewBroadcast => 'Te agregó a una nueva transmisión';

  @override
  String get download => 'Descargar';

  @override
  String get copy => 'Copiar';

  @override
  String get info => 'Información';

  @override
  String get share => 'Compartir';

  @override
  String get forward => 'Adelante';

  @override
  String get reply => 'Responder';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Eliminar de todos';

  @override
  String get deleteFromMe => 'Eliminar de mi';

  @override
  String get downloading => 'Descarga ...';

  @override
  String get fileHasBeenSavedTo => 'El archivo se ha guardado en';

  @override
  String get online => 'En línea';

  @override
  String get youDontHaveAccess => 'No tienes acceso';

  @override
  String get replyToYourSelf => 'Responder a ti mismo';

  @override
  String get repliedToYourSelf => 'Replic para ti mismo';

  @override
  String get audioCall => 'Audio llamado';

  @override
  String get ring => 'Anillo';

  @override
  String get canceled => 'Cancelado';

  @override
  String get timeout => 'Se acabó el tiempo';

  @override
  String get rejected => 'Rechazado';

  @override
  String get finished => 'Finalizado';

  @override
  String get inCall => 'En la llamada';

  @override
  String get sessionEnd => 'Sesión final';

  @override
  String get yesterday => 'Ayer';

  @override
  String get today => 'Hoy';

  @override
  String get textFieldHint => 'Escriba un mensaje ...';

  @override
  String get files => 'Pauta';

  @override
  String get location => 'Alquiler';

  @override
  String get shareMediaAndLocation => 'Compartir medios y alquiler';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Hay tamaño de video más grande que el tamaño permitido';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Hay que el archivo tiene tamaño más grande que el tamaño permitido';

  @override
  String get makeCall => 'Hacer llamadas';

  @override
  String get areYouWantToMakeVideoCall => '¿Quieres hacer videollamadas?';

  @override
  String get areYouWantToMakeVoiceCall => '¿Quieres hacer una llamada de voz?';

  @override
  String get vMessagesInfoTrans => 'Mensajes de información';

  @override
  String get star => 'Estrella';

  @override
  String get minutes => 'Minutos';

  @override
  String get sendMessage => 'Mensaje';

  @override
  String get deleteUser => 'Eliminar usuario';

  @override
  String get actions => 'Comportamiento';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Estás a punto de eliminar a este usuario de su lista';

  @override
  String get updateBroadcastTitle => 'Actualizar título de transmisión';

  @override
  String get usersAddedSuccessfully => 'Los usuarios agregaron con éxito';

  @override
  String get broadcastSettings => 'Configuración de transmisión';

  @override
  String get addParticipants => 'Agregar participantes';

  @override
  String get broadcastParticipants => 'Participantes de transmisión';

  @override
  String get updateGroupDescription => 'Actualizar la descripción del grupo';

  @override
  String get updateGroupTitle => 'Actualizar el título del grupo';

  @override
  String get groupSettings => 'Configuración grupal';

  @override
  String get description => 'Descripción';

  @override
  String get muteNotifications => 'Notificaciones mudas';

  @override
  String get groupParticipants => 'Grupo de participantes';

  @override
  String get blockUser => 'Bloque de usuarios';

  @override
  String get areYouSureToBlock => '¿Estás seguro de bloquear?';

  @override
  String get userPage => 'Página de usuario';

  @override
  String get starMessage => 'Mensaje de estrella';

  @override
  String get showMedia => 'Programa de medios';

  @override
  String get reportUser => 'Informe usuario';

  @override
  String get groupName => 'Nombre de grupo';

  @override
  String get changeSubject => 'Cambiar sujeto';

  @override
  String get titleIsRequired => 'Se requiere título';

  @override
  String get createBroadcast => 'Crear transmisión';

  @override
  String get broadcastName => 'Nombre de transmisión';

  @override
  String get createGroup => 'Crear grupo';

  @override
  String get forgetPassword => '¿Olvidar la contraseña?';

  @override
  String get globalSearch => 'Búsqueda global';

  @override
  String get dismissesToMember => 'Despedida al miembro';

  @override
  String get setToAdmin => 'Establecer en Admin';

  @override
  String get kickMember => 'Miembro de la patada';

  @override
  String get youAreAboutToDismissesToMember =>
      'Estás a punto de disfmo al miembro';

  @override
  String get youAreAboutToKick => 'Estás a punto de patear';

  @override
  String get groupMembers => 'Miembros del grupo';

  @override
  String get tapForPhoto => 'Toque para la foto';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'Recomendamos altos descargar esta actualización';

  @override
  String get newGroup => 'Nuevo grupo';

  @override
  String get newBroadcast => 'Nueva transmisión';

  @override
  String get starredMessage => 'Mensaje protagonizado';

  @override
  String get settings => 'Configuraciones';

  @override
  String get chats => 'Gatos';

  @override
  String get recentUpdates => 'Actualizaciones recientes';

  @override
  String get startChat => 'Inicie el gato';

  @override
  String get newUpdateIsAvailable => 'Una nueva actualización está disponible';

  @override
  String get emailNotValid => 'Correo electrónico no válido';

  @override
  String get passwordMustHaveValue => 'La contraseña debe tener valor';

  @override
  String get error => 'Error';

  @override
  String get password => 'Contraseña';

  @override
  String get login => 'Acceso';

  @override
  String get needNewAccount => '¿Necesitas una nueva cuenta?';

  @override
  String get register => 'Registro';

  @override
  String get nameMustHaveValue => 'El nombre debe tener valor';

  @override
  String get passwordNotMatch => 'Contraseña no coincide';

  @override
  String get name => 'Nombre';

  @override
  String get email => 'Correo electrónico';

  @override
  String get confirmPassword => 'confirmar Contraseña';

  @override
  String get alreadyHaveAnAccount => '¿Ya tienes una cuenta?';

  @override
  String get logOut => 'Finalizar la sesión';

  @override
  String get back => 'Atrás';

  @override
  String get sendCodeToMyEmail => 'Enviar código a mi correo electrónico';

  @override
  String get invalidLoginData => 'Datos de inicio de sesión no válidos';

  @override
  String get userEmailNotFound =>
      'Correo electrónico del usuario no encontrado';

  @override
  String get yourAccountBlocked => 'Tu cumpleaños ha sido prohibido';

  @override
  String get yourAccountDeleted => 'Tu cumpleaños ha sido eliminado';

  @override
  String get userAlreadyRegister => 'El usuario ya se registra';

  @override
  String get codeHasBeenExpired => 'Ha sido código de caducidad';

  @override
  String get invalidCode => 'Código no válido';

  @override
  String get whileAuthCanFindYou =>
      'Mientras que la autenticación no puede encontrarte';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'Estado de registro de usuario aún no aceptado';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'El dispositivo ha sido inicio de sesión desde todos los dispositivos';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'Dispositivo del dispositivo del usuario Dispositivo eliminado';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'Ningún código ha sido sen a usted para verificar su correo electrónico';

  @override
  String get roomAlreadyInCall => 'Habitación ya en llamada';

  @override
  String get peerUserInCallNow => 'Usuario en la llamada ahora';

  @override
  String get callNotAllowed => 'Llamar no permitido';

  @override
  String get peerUserDeviceOffline =>
      'Dispositivo de usuario de par sin conexión';

  @override
  String get emailMustBeValid => 'El correo electrónico debe ser válido';

  @override
  String get wait2MinutesToSendMail => 'Espere 2 minutos para enviar correo';

  @override
  String get codeMustEqualToSixNumbers => 'El código debe igual a seis números';

  @override
  String get newPasswordMustHaveValue => 'Nueva contraseña debe tener valor';

  @override
  String get confirmPasswordMustHaveValue =>
      'Confirmar la contraseña debe tener valor';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Congregaciones Su cuenta ha sido aceptada';

  @override
  String get yourAccountIsUnderReview => 'Tu nacimiento está bajo revisión';

  @override
  String get waitingList => 'Lista de espera';

  @override
  String get welcome => 'Bienvenido';

  @override
  String get retry => 'Rever';

  @override
  String get deleteMember => 'Eliminar miembro';

  @override
  String get profile => 'Perfil';

  @override
  String get broadcastInfo => 'Información de transmisión';

  @override
  String get updateTitle => 'Título de actualización';

  @override
  String get members => 'Colocar';

  @override
  String get addMembers => 'Agregar miembros';

  @override
  String get success => 'Éxito';

  @override
  String get media => 'Medios de comunicación';

  @override
  String get docs => 'Documento';

  @override
  String get links => 'Campo de golf';

  @override
  String get soon => 'Pronto';

  @override
  String get unStar => 'Una estrella';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Actualizar la descripción del grupo actualizará a todos los miembros del grupo';

  @override
  String get updateNickname => 'ACTUALIZACIÓN';

  @override
  String get groupInfo => 'Información grupal';

  @override
  String get youNotParticipantInThisGroup => 'No participa en este grupo';

  @override
  String get search => 'Buscar';

  @override
  String get mediaLinksAndDocs => 'Medios, enlaces y documentos';

  @override
  String get starredMessages => 'Mensajes protagonizados';

  @override
  String get nickname => 'Apodo';

  @override
  String get none => 'Ninguno';

  @override
  String get yes => 'Sí';

  @override
  String get no => 'No';

  @override
  String get exitGroup => 'Grupo de salida';

  @override
  String get clickToAddGroupDescription =>
      'Haga clic para agregar la descripción del grupo';

  @override
  String get unBlockUser => 'Usuario de ablock';

  @override
  String get areYouSureToUnBlock => '¿Estás seguro de ablock?';

  @override
  String get contactInfo => 'Información de contacto';

  @override
  String get audio => 'Audio';

  @override
  String get video => 'Video';

  @override
  String get hiIamUse => 'Hola iam usando';

  @override
  String get on => 'Nosotros';

  @override
  String get off => 'Apagado';

  @override
  String get unBlock => 'Un bloque';

  @override
  String get block => 'Bloquear';

  @override
  String get chooseAtLestOneMember => 'Elija al menos un miembro';

  @override
  String get close => 'Cerrado';

  @override
  String get next => 'Próximo';

  @override
  String get appMembers => 'Miembros de la aplicación';

  @override
  String get create => 'Crear';

  @override
  String get upgradeToAdmin => 'Actualizar al administrador';

  @override
  String get update => 'Actualizar';

  @override
  String get deleteChat => 'Gato eliminar';

  @override
  String get clearChat => 'Gato claro';

  @override
  String get showHistory => 'Exhibir la historia';

  @override
  String get groupIcon => 'Icono grupal';

  @override
  String get tapToSelectAnIcon => 'Toque para seleccionar el icono de año';

  @override
  String get groupDescription => 'Descripción del grupo';

  @override
  String get more => 'Más';

  @override
  String get messageInfo => 'Mensaje de información';

  @override
  String get successfullyDownloadedIn => 'Descargado con éxito en';

  @override
  String get delivered => 'Entregado';

  @override
  String get read => 'Leer';

  @override
  String get orLoginWith => 'Gold Iniciar sesión con';

  @override
  String get resetPassword => 'Restablecer contraseña';

  @override
  String get otpCode => 'Código OTP';

  @override
  String get newPassword => 'Nueva contraseña';

  @override
  String get areYouSure => '¿Está seguro?';

  @override
  String get broadcastMembers => 'Miembros de la transmisión';

  @override
  String get phone => 'Teléfono';

  @override
  String get users => 'Usuarios';

  @override
  String get calls => 'Llamadas';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Estás a punto de cerrar sesión desde este acuerdo';

  @override
  String get noUpdatesAvailableNow =>
      'No hay actualizaciones disponibles ahora';

  @override
  String get dataPrivacy => 'Privacidad de datos';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      '¡Todos los datos han sido una copia de seguridad de que no desea administrar guardar los datos por usted mismo!Si inicia sesión e inicia sesión nuevamente, verá todos los gatos lo mismo para la versión web';

  @override
  String get account => 'Nacimiento';

  @override
  String get linkedDevices => 'Dispositivos vinculados';

  @override
  String get storageAndData => 'Almacenamiento y datos';

  @override
  String get tellAFriend => 'Dile a un amigo';

  @override
  String get help => 'Ayuda';

  @override
  String get blockedUsers => 'Usuarios de bloque';

  @override
  String get inAppAlerts => 'En alertas de aplicaciones';

  @override
  String get language => 'Idioma';

  @override
  String get adminNotification => 'Notificación de administración';

  @override
  String get checkForUpdates => 'Verifique las actualizaciones';

  @override
  String get linkByQrCode => 'Enlace por código QR';

  @override
  String get deviceStatus => 'Estado del dispositivo';

  @override
  String get desktopAndOtherDevices => 'Escritorio y otros dispositivos';

  @override
  String get linkADeviceSoon => 'Enlace un dispositivo (pronto)';

  @override
  String get lastActiveFrom => 'Último activo desde';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Toque un dispositivo para editar o iniciar sesión.';

  @override
  String get contactUs => 'Contacto de EE. UU.';

  @override
  String get supportChatSoon => 'Soporte de gatos (pronto)';

  @override
  String get updateYourName => 'Actualiza tu nombre';

  @override
  String get updateYourBio => 'Actualiza tu orgánico';

  @override
  String get edit => 'Editar';

  @override
  String get about => 'Acerca de';

  @override
  String get oldPassword => 'Contraseña anterior';

  @override
  String get deleteMyAccount => 'Eliminar mi cuenta';

  @override
  String get passwordHasBeenChanged => 'Se ha cambiado la contraseña';

  @override
  String get logoutFromAllDevices => '¿INCARGE de todos los dispositivos?';

  @override
  String get updateYourPassword => 'Actualiza tu contraseña';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Ingrese su nombre y agregue una foto de perfil opcional';

  @override
  String get privacyPolicy => 'política de privacidad';

  @override
  String get chat => 'Gato';

  @override
  String get send => 'Enviar';

  @override
  String get reportHasBeenSubmitted => 'Su aplazamiento ha sido enviado';

  @override
  String get offline => 'Desconectado';

  @override
  String get harassmentOrBullyingDescription =>
      'Acoso o intimidación: esta opción permite a los usuarios informar a las personas que se dirigen a ellos u otros con mensajes de acoso, amenazas u otras formas de intimidación.';

  @override
  String get spamOrScamDescription =>
      'Spam o estafa: esta opción sería para utilizar una cuenta de aplazamiento que envía mensajes de spam, anuncios no solicitados o intentan estafar a los Oters.';

  @override
  String get areYouSureToReportUserToAdmin =>
      '¿Seguro que enviará un informe sobre este usuario al administrador?';

  @override
  String get groupWith => 'Agrupar con';

  @override
  String get inappropriateContentDescription =>
      'Contenido inapropiado: los usuarios pueden seleccionar esta opción para informar cualquier material explícito sexual, discurso de odio u otro contenido que el estándar de la comunidad violenta.';

  @override
  String get otherCategoryDescription =>
      'Otro: esta categoría de Catch-All se puede usar para violaciones que se ajustan fácilmente a las categorías de Abovers.Puede ser útil incluir un cuadro de texto para que los usuarios proporcionen detalles adicionales.';

  @override
  String get explainWhatHappens => 'Explica aquí lo que pasa';

  @override
  String get loginAgain => '¡Inicie sesión de nuevo!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      '¡Su sesión es y finalizó por favor inicie sesión nuevamente!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Estás a punto de bloquear a este usuario.¡Puedes conocerlo gatos y no puedes agregarlo a grupos o transmitir!';

  @override
  String
      get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
          'Estás a punto de eliminar tu nacimiento';

  @override
  String get admin => 'Administración';

  @override
  String get member => 'Colocar';

  @override
  String get creator => 'Creador';

  @override
  String get currentDevice => 'Dispositivo actual';

  @override
  String get visits => 'Visitas';

  @override
  String get chooseRoom => 'Elija habitación';

  @override
  String get deleteThisDeviceDesc =>
      'Eliminar este dispositivo significa que inicia sesión instantáneamente este dispositivo';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Estás a punto de actualizar al administrador';

  @override
  String get microphonePermissionMustBeAccepted =>
      'El permiso de micrófono debe ser aceptado';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'El micrófono y el permiso de la cámara deben';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Iniciar sesión ahora permitido.Vuelva a intentarlo más tarde.';

  @override
  String get dashboard => 'Panel';

  @override
  String get notification => 'Notificación';

  @override
  String get total => 'Total';

  @override
  String get blocked => 'Obstruido';

  @override
  String get deleted => 'Eliminado';

  @override
  String get accepted => 'Aceptado';

  @override
  String get notAccepted => 'No aceptar';

  @override
  String get web => 'Web';

  @override
  String get android => 'Androide';

  @override
  String get macOs => 'macosa';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Otro';

  @override
  String get totalVisits => 'Visitas totales';

  @override
  String get totalMessages => 'Mensajes totales';

  @override
  String get textMessages => 'Mensajes de texto';

  @override
  String get imageMessages => 'Mensajes de imagen';

  @override
  String get videoMessages => 'Mensajes de video';

  @override
  String get voiceMessages => 'Mensajes de voz';

  @override
  String get fileMessages => 'Mensajes de archivo';

  @override
  String get infoMessages => 'Mensajes de información';

  @override
  String get voiceCallMessages => 'Mensajes de llamadas de voz';

  @override
  String get videoCallMessages => 'Llamar a los mensajes de video';

  @override
  String get locationMessages => 'Alquiler de mensajes';

  @override
  String get directChat => 'Gato directo';

  @override
  String get group => 'Grupo';

  @override
  String get broadcast => 'Transmisión';

  @override
  String get messageCounter => 'Mostrador';

  @override
  String get roomCounter => 'Mostrador';

  @override
  String get countries => 'Manejo';

  @override
  String get devices => 'Dispositivos';

  @override
  String get notificationTitle => 'Notificación de título';

  @override
  String get notificationDescription => 'Notificación de descripción';

  @override
  String get notificationsPage => 'Página de notificaciones';

  @override
  String get updateFeedBackEmail =>
      'Actualizar el correo electrónico de comentarios';

  @override
  String get setMaxMessageForwardAndShare =>
      'Establezca el mensaje máximo hacia adelante y comparta';

  @override
  String get setNewPrivacyPolicyUrl =>
      'Establecer una nueva URL de política de privacidad';

  @override
  String get forgetPasswordExpireTime =>
      'Olvidar la contraseña expira el tiempo';

  @override
  String get callTimeoutInSeconds => 'Llame al tiempo de espera en segundos';

  @override
  String get setMaxGroupMembers => 'Establecer miembros del grupo Max';

  @override
  String get setMaxBroadcastMembers => 'Establecer miembros de transmisión Max';

  @override
  String get allowCalls => 'Permitir llamadas';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Si esta opción está habilitada, se permitirán la llamada de video y voz.';

  @override
  String get allowAds => 'Permitir anuncios';

  @override
  String get allowMobileLogin => 'Permitir el inicio de sesión móvil';

  @override
  String get allowWebLogin => 'Permitir el inicio de sesión web';

  @override
  String get messages => 'Mensajes';

  @override
  String get appleStoreAppUrl => 'URL de la aplicación Apple Store';

  @override
  String get googlePlayAppUrl => 'URL de la aplicación Google Play';

  @override
  String get privacyUrl => 'URL de privacidad';

  @override
  String get feedBackEmail => 'Correo electrónico de comentarios';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Si esta opción está deshabilitada, se bloqueará el envío de archivos, imágenes, videos y alquiler CAT.';

  @override
  String get allowSendMedia => 'Permitir enviar medios';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Si esta opción está deshabilitada, la creación de la transmisión de chat se bloqueará.';

  @override
  String get allowCreateBroadcast => 'Permitir crear transmisión';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Si esta opción está deshabilitada, la creación de grupos de chat se bloqueará.';

  @override
  String get allowCreateGroups => 'Permitir crear grupos';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Si esta opción está deshabilitada, se bloqueará el inicio de sesión o registro de escritorio (Windows, Mac).';

  @override
  String get allowDesktopLogin => 'Permitir inicio de sesión de escritorio';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Si esta opción está deshabilitada, el inicio de sesión o registro web se bloqueará.';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'Si esta opción está deshabilitada, el inicio de sesión o el registro móvil se bloqueará solo en Android e iOS.';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'Si esta opción está habilitada, el banner de Google ADS aparecerá en gatos.';

  @override
  String get userProfile => 'Perfil de usuario';

  @override
  String get userInfo => 'Información de usuario';

  @override
  String get fullName => 'Nombre completo';

  @override
  String get bio => 'Orgánico';

  @override
  String get noBio => 'No biografía';

  @override
  String get verifiedAt => 'Verificado en';

  @override
  String get country => 'País';

  @override
  String get registerStatus => 'Estado de registro';

  @override
  String get registerMethod => 'Método Registrarse';

  @override
  String get banTo => 'Prohibir';

  @override
  String get deletedAt => 'Eliminado en';

  @override
  String get createdAt => 'Creado a';

  @override
  String get updatedAt => 'Actualizado en';

  @override
  String get reports => 'Aplazamiento';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Haga clic para ver todos los detalles de los dispositivos de usuario';

  @override
  String get allDeletedMessages => 'Todos los mensajes eliminados';

  @override
  String get voiceCallMessage => 'Mensaje de llamadas de voz';

  @override
  String get totalRooms => 'Habitaciones totales';

  @override
  String get directRooms => 'Habitaciones directas';

  @override
  String get userAction => 'Acción del usuario';

  @override
  String get status => 'Estado';

  @override
  String get joinedAt => 'Unido a';

  @override
  String get saveLogin => 'Guardar inicio de sesión';

  @override
  String get passwordIsRequired => 'Se requiere contraseña';

  @override
  String get verified => 'Verificado';

  @override
  String get pending => 'Colgante';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Se requiere una descripción';

  @override
  String get seconds => 'Artículos de segunda clase';

  @override
  String get clickToSeeAllUserInformations =>
      'Haga clic para ver toda la información del usuario';

  @override
  String get clickToSeeAllUserCountries =>
      'Haga clic para ver a todos los países de los usuarios';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Haga clic para ver todos los detalles de los mensajes de usuario';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Haga clic para ver todos los detalles de las habitaciones de usuario';

  @override
  String get clickToSeeAllUserReports =>
      'Haga clic para ver todos los informes de los usuarios';

  @override
  String get banAt => 'Prohibir';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Ahora inicia sesión como administrador de solo lectura.Todas las ediciones que realice no se aplicarán debido a que esta es una versión de prueba.';

  @override
  String get createStory => 'Crear una historia';

  @override
  String get writeACaption => 'Escribe un subtítulo ...';

  @override
  String get storyCreatedSuccessfully => 'Historia creada con éxito';

  @override
  String get stories => 'Historias';

  @override
  String get clear => 'Claro';

  @override
  String get clearCallsConfirm => 'Llamadas de claro confirmar';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Elija cómo funciona la descarga automática';

  @override
  String get whenUsingMobileData => 'Cuando se usa datos móviles';

  @override
  String get whenUsingWifi => 'Cuando se usa Wi-Fi';

  @override
  String get image => 'Imagen';

  @override
  String get myPrivacy => 'Mi privacidad';

  @override
  String get createTextStory => 'Crear historia de texto';

  @override
  String get createMediaStory => 'Crear historia de los medios';

  @override
  String get camera => 'Cámara';

  @override
  String get gallery => 'Galería';

  @override
  String get recentUpdate => 'Actualización reciente';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Agregar nueva historia';

  @override
  String get updateYourProfile => 'Actualiza tu perfil';

  @override
  String get configureYourAccountPrivacy =>
      'Configurar la privacidad de su cuenta';

  @override
  String get youInPublicSearch => 'Tú en la búsqueda pública';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Su perfil aparece en la búsqueda pública y la adición de grupos';

  @override
  String get yourLastSeen => 'Tu último visto';

  @override
  String get yourLastSeenInChats => 'Tu último visto en gatos';

  @override
  String get startNewChatWithYou => 'Empiece a chat nuevo contigo';

  @override
  String get yourStory => 'Tu historia';

  @override
  String get forRequest => 'Por solicitud';

  @override
  String get public => 'Audiencia';

  @override
  String get createYourStory => 'Crea tu historia';

  @override
  String get shareYourStatus => 'Comparte tu estado';

  @override
  String get oneSeenMessage => 'Un mensaje';

  @override
  String get messageHasBeenViewed => 'Se ha visto el mensaje';

  @override
  String get clickToSee => 'Haga clic para ver';

  @override
  String get images => 'Imágenes';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String areYouSureRemoveAccount(Object name) {
    return 'Are you sure you want to remove the account for $name?';
  }

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';
}
