// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get done => '完了';

  @override
  String get loading => '読み込み中...';

  @override
  String get messageHasBeenDeleted => 'メッセージが削除されました';

  @override
  String get mute => 'ミュート';

  @override
  String get cancel => 'キャンセル';

  @override
  String get typing => '入力中...';

  @override
  String get ok => 'OK';

  @override
  String get recording => '録音中...';

  @override
  String get connecting => '接続中...';

  @override
  String get deleteYouCopy => 'コピーを削除';

  @override
  String get unMute => 'ミュート解除';

  @override
  String get delete => '削除';

  @override
  String get report => '通報';

  @override
  String get leaveGroup => 'グループを退席';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'コピーを許可しますか？このアクションは元に戻せません';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'このグループを退席しますか？このアクションは元に戻せません';

  @override
  String get leaveGroupAndDeleteYourMessageCopy => 'グループを退席してメッセージのコピーを削除';

  @override
  String get vMessageInfoTrans => 'メッセージ情報';

  @override
  String get updateTitleTo => 'タイトルを更新';

  @override
  String get updateImage => '画像を更新';

  @override
  String get joinedBy => '参加者：';

  @override
  String get promotedToAdminBy => '管理者に昇格：';

  @override
  String get dismissedToMemberBy => 'メンバーに降格：';

  @override
  String get leftTheGroup => 'グループを退席';

  @override
  String get you => 'あなた';

  @override
  String get kickedBy => 'キックされました：';

  @override
  String get groupCreatedBy => 'グループ作成者：';

  @override
  String get addedYouToNewBroadcast => '新しいブロードキャストに追加されました';

  @override
  String get download => 'ダウンロード';

  @override
  String get copy => 'コピー';

  @override
  String get info => '情報';

  @override
  String get share => '共有';

  @override
  String get forward => '転送';

  @override
  String get reply => '返信';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => '全てから削除';

  @override
  String get deleteFromMe => '自分から削除';

  @override
  String get downloading => 'ダウンロード中...';

  @override
  String get fileHasBeenSavedTo => 'ファイルは保存されました：';

  @override
  String get online => 'オンライン';

  @override
  String get youDontHaveAccess => 'アクセス権がありません';

  @override
  String get replyToYourSelf => '自分に返信';

  @override
  String get repliedToYourSelf => '自分に返信しました';

  @override
  String get audioCall => '音声通話';

  @override
  String get ring => '呼び出し中...';

  @override
  String get canceled => 'キャンセル';

  @override
  String get timeout => 'タイムアウト';

  @override
  String get rejected => '拒否';

  @override
  String get finished => '終了';

  @override
  String get inCall => '通話中';

  @override
  String get sessionEnd => 'セッション終了';

  @override
  String get yesterday => '昨日';

  @override
  String get today => '今日';

  @override
  String get textFieldHint => 'メッセージを入力...';

  @override
  String get files => 'ファイル';

  @override
  String get location => '位置情報';

  @override
  String get shareMediaAndLocation => 'メディアと位置情報を共有';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize => '許容サイズよりも大きいビデオがあります';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize => '許容サイズよりも大きいファイルがあります';

  @override
  String get makeCall => '通話を開始';

  @override
  String get areYouWantToMakeVideoCall => 'ビデオ通話を開始しますか？';

  @override
  String get areYouWantToMakeVoiceCall => '音声通話を開始しますか？';

  @override
  String get vMessagesInfoTrans => 'メッセージ情報';

  @override
  String get star => 'スター';

  @override
  String get minutes => '分';

  @override
  String get sendMessage => 'メッセージを送信';

  @override
  String get deleteUser => 'ユーザーを削除';

  @override
  String get actions => 'アクション';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'このユーザーをリストから削除しようとしています';

  @override
  String get updateBroadcastTitle => 'ブロードキャストのタイトルを更新';

  @override
  String get usersAddedSuccessfully => 'ユーザーが正常に追加されました';

  @override
  String get broadcastSettings => 'ブロードキャストの設定';

  @override
  String get addParticipants => '参加者を追加';

  @override
  String get broadcastParticipants => 'ブロードキャスト参加者';

  @override
  String get updateGroupDescription => 'グループの説明を更新';

  @override
  String get updateGroupTitle => 'グループのタイトルを更新';

  @override
  String get groupSettings => 'グループの設定';

  @override
  String get description => '説明';

  @override
  String get muteNotifications => '通知をミュート';

  @override
  String get groupParticipants => 'グループ参加者';

  @override
  String get blockUser => 'ユーザーをブロック';

  @override
  String get areYouSureToBlock => 'ユーザーをブロックしますか：';

  @override
  String get userPage => 'ユーザーページ';

  @override
  String get starMessage => 'メッセージをスター付け';

  @override
  String get showMedia => 'メディアを表示';

  @override
  String get reportUser => 'ユーザーを通報';

  @override
  String get groupName => 'グループ名';

  @override
  String get changeSubject => 'サブジェクトを変更';

  @override
  String get titleIsRequired => 'タイトルが必要です';

  @override
  String get createBroadcast => 'ブロードキャストを作成';

  @override
  String get broadcastName => 'ブロードキャスト名';

  @override
  String get createGroup => 'グループを作成';

  @override
  String get forgetPassword => 'パスワードを忘れました';

  @override
  String get globalSearch => 'グローバル検索';

  @override
  String get dismissesToMember => 'メンバーに降格';

  @override
  String get setToAdmin => '管理者に設定';

  @override
  String get kickMember => 'メンバーをキック';

  @override
  String get youAreAboutToDismissesToMember => 'メンバーを降格しようとしています';

  @override
  String get youAreAboutToKick => 'キックしようとしています';

  @override
  String get groupMembers => 'グループメンバー';

  @override
  String get tapForPhoto => '写真をタップ';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'We high recommend to download this update';

  @override
  String get newGroup => '新しいグループ';

  @override
  String get newBroadcast => '新しいブロードキャスト';

  @override
  String get starredMessage => 'スター付きメッセージ';

  @override
  String get settings => '設定';

  @override
  String get chats => 'チャット';

  @override
  String get recentUpdates => '最近のアップデート';

  @override
  String get startChat => 'チャットを開始';

  @override
  String get newUpdateIsAvailable => '新しいアップデートが利用可能です';

  @override
  String get emailNotValid => 'メールアドレスが無効です';

  @override
  String get passwordMustHaveValue => 'パスワードは必須です';

  @override
  String get error => 'エラー';

  @override
  String get password => 'パスワード';

  @override
  String get login => 'ログイン';

  @override
  String get needNewAccount => '新しいアカウントが必要ですか？';

  @override
  String get register => '登録';

  @override
  String get nameMustHaveValue => '名前は必須です';

  @override
  String get passwordNotMatch => 'パスワードが一致しません';

  @override
  String get name => '名前';

  @override
  String get email => 'メールアドレス';

  @override
  String get confirmPassword => 'パスワードの確認';

  @override
  String get alreadyHaveAnAccount => '既にアカウントをお持ちですか？';

  @override
  String get logOut => 'ログアウト';

  @override
  String get back => '戻る';

  @override
  String get sendCodeToMyEmail => 'コードを私のメールに送信';

  @override
  String get invalidLoginData => '無効なログインデータ';

  @override
  String get userEmailNotFound => 'ユーザーメールアドレスが見つかりません';

  @override
  String get yourAccountBlocked => 'アカウントがブロックされました';

  @override
  String get yourAccountDeleted => 'アカウントが削除されました';

  @override
  String get userAlreadyRegister => 'ユーザーは既に登録済みです';

  @override
  String get codeHasBeenExpired => 'コードは期限切れです';

  @override
  String get invalidCode => '無効なコード';

  @override
  String get whileAuthCanFindYou => 'While authentication cannot find you';

  @override
  String get userRegisterStatusNotAcceptedYet => 'ユーザー登録ステータスはまだ受け入れられていません';

  @override
  String get deviceHasBeenLogoutFromAllDevices => 'デバイスはすべてのデバイスからログアウトしました';

  @override
  String get userDeviceSessionEndDeviceDeleted => 'ユーザーデバイスセッション終了、デバイス削除';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'No code has been send to you to verify your email';

  @override
  String get roomAlreadyInCall => 'ルームは既に通話中です';

  @override
  String get peerUserInCallNow => '相手のユーザーは現在通話中です';

  @override
  String get callNotAllowed => '通話が許可されていません';

  @override
  String get peerUserDeviceOffline => '相手のユーザーデバイスがオフラインです';

  @override
  String get emailMustBeValid => 'メールアドレスは有効である必要があります';

  @override
  String get wait2MinutesToSendMail => 'メールを送信するには2分待ってください';

  @override
  String get codeMustEqualToSixNumbers => 'Code must equal to six numbers';

  @override
  String get newPasswordMustHaveValue => '新しいパスワードは必須です';

  @override
  String get confirmPasswordMustHaveValue => 'パスワードの確認は必須です';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'おめでとうございます、アカウントが承認されました';

  @override
  String get yourAccountIsUnderReview => 'アカウントは審査中です';

  @override
  String get waitingList => '待機リスト';

  @override
  String get welcome => 'ようこそ';

  @override
  String get retry => '再試行';

  @override
  String get deleteMember => 'メンバーを削除';

  @override
  String get profile => 'プロフィール';

  @override
  String get broadcastInfo => 'ブロードキャスト情報';

  @override
  String get updateTitle => 'タイトルを更新';

  @override
  String get members => 'メンバー';

  @override
  String get addMembers => 'メンバーを追加';

  @override
  String get success => '成功';

  @override
  String get media => 'メディア';

  @override
  String get docs => 'ドキュメント';

  @override
  String get links => 'リンク';

  @override
  String get soon => '近日公開';

  @override
  String get unStar => 'スターを解除';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'グループの説明を更新すると、すべてのグループメンバーに適用されます';

  @override
  String get updateNickname => 'ニックネームを更新';

  @override
  String get groupInfo => 'グループ情報';

  @override
  String get youNotParticipantInThisGroup => 'このグループの参加者ではありません';

  @override
  String get search => '検索';

  @override
  String get mediaLinksAndDocs => 'メディア、リンク、およびドキュメント';

  @override
  String get starredMessages => 'スター付きメッセージ';

  @override
  String get nickname => 'ニックネーム';

  @override
  String get none => 'なし';

  @override
  String get yes => 'はい';

  @override
  String get no => 'いいえ';

  @override
  String get exitGroup => 'グループを退出';

  @override
  String get clickToAddGroupDescription => 'クリックしてグループ説明を追加';

  @override
  String get unBlockUser => 'ユーザーのブロックを解除';

  @override
  String get areYouSureToUnBlock => '本当にブロックを解除しますか？';

  @override
  String get contactInfo => '連絡先情報';

  @override
  String get audio => 'オーディオ';

  @override
  String get video => 'ビデオ';

  @override
  String get hiIamUse => 'こんにちは、私は使います';

  @override
  String get on => 'オン';

  @override
  String get off => 'オフ';

  @override
  String get unBlock => 'ブロック解除';

  @override
  String get block => 'ブロック';

  @override
  String get chooseAtLestOneMember => 'Choose at lest one member';

  @override
  String get close => '閉じる';

  @override
  String get next => '次へ';

  @override
  String get appMembers => 'アプリのメンバー';

  @override
  String get create => '作成';

  @override
  String get upgradeToAdmin => '管理者に昇格';

  @override
  String get update => '更新';

  @override
  String get deleteChat => 'チャットを削除';

  @override
  String get clearChat => 'チャットをクリア';

  @override
  String get showHistory => '履歴を表示';

  @override
  String get groupIcon => 'グループアイコン';

  @override
  String get tapToSelectAnIcon => 'アイコンを選択するにはタップ';

  @override
  String get groupDescription => 'グループ説明';

  @override
  String get more => '詳細';

  @override
  String get messageInfo => 'メッセージ情報';

  @override
  String get successfullyDownloadedIn => 'ダウンロードが正常に完了しました';

  @override
  String get delivered => '配信済み';

  @override
  String get read => '既読';

  @override
  String get orLoginWith => 'または次でログイン';

  @override
  String get resetPassword => 'パスワードをリセット';

  @override
  String get otpCode => 'OTPコード';

  @override
  String get newPassword => '新しいパスワード';

  @override
  String get areYouSure => '本当に確認しますか？';

  @override
  String get broadcastMembers => 'ブロードキャストメンバー';

  @override
  String get phone => '電話';

  @override
  String get users => 'ユーザー';

  @override
  String get calls => '通話';

  @override
  String get yourAreAboutToLogoutFromThisAccount => 'このアカウントからログアウトしようとしています';

  @override
  String get noUpdatesAvailableNow => '現在、アップデートは利用できません';

  @override
  String get dataPrivacy => 'データプライバシー';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'すべてのデータはバックアップされ、データの管理は必要ありません。ログアウトして再ログインすると、ウェブバージョンと同じチャットが表示されます';

  @override
  String get account => 'アカウント';

  @override
  String get linkedDevices => 'リンクされたデバイス';

  @override
  String get storageAndData => 'ストレージとデータ';

  @override
  String get tellAFriend => '友達に教える';

  @override
  String get help => 'ヘルプ';

  @override
  String get blockedUsers => 'ブロックされたユーザー';

  @override
  String get inAppAlerts => 'アプリ内アラート';

  @override
  String get language => '言語';

  @override
  String get adminNotification => '管理者通知';

  @override
  String get checkForUpdates => 'アップデートの確認';

  @override
  String get linkByQrCode => 'QRコードでリンク';

  @override
  String get deviceStatus => 'デバイスの状態';

  @override
  String get desktopAndOtherDevices => 'デスクトップおよびその他のデバイス';

  @override
  String get linkADeviceSoon => 'デバイスをリンクする（近日公開予定）';

  @override
  String get lastActiveFrom => '最後にアクティブ';

  @override
  String get tapADeviceToEditOrLogOut => 'デバイスをタップして編集またはログアウト';

  @override
  String get contactUs => 'お問い合わせ';

  @override
  String get supportChatSoon => 'サポートチャット（近日公開予定）';

  @override
  String get updateYourName => '名前を更新';

  @override
  String get updateYourBio => 'バイオを更新';

  @override
  String get edit => '編集';

  @override
  String get about => '情報';

  @override
  String get oldPassword => '古いパスワード';

  @override
  String get deleteMyAccount => 'アカウントを削除';

  @override
  String get passwordHasBeenChanged => 'パスワードが変更されました';

  @override
  String get logoutFromAllDevices => 'すべてのデバイスからログアウトしますか？';

  @override
  String get updateYourPassword => 'パスワードを更新';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      '名前を入力し、オプションのプロフィール画像を追加';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get chat => 'チャット';

  @override
  String get send => '送信';

  @override
  String get reportHasBeenSubmitted => 'レポートが送信されました';

  @override
  String get offline => 'オフライン';

  @override
  String get harassmentOrBullyingDescription =>
      '嫌がらせやいじめ：このオプションを使用して、嫌がらせメッセージ、脅迫、またはその他のいじめ行為を行っている個人を報告できます。';

  @override
  String get spamOrScamDescription =>
      'スパムまたは詐欺：このオプションは、スパムメッセージ、不要な広告を送信しているアカウント、または他のユーザーを騙そうとしているアカウントを報告するためのものです。';

  @override
  String get areYouSureToReportUserToAdmin => 'このユーザーについて管理者に報告する確認していますか？';

  @override
  String get groupWith => 'グループと';

  @override
  String get inappropriateContentDescription =>
      '不適切なコンテンツ：ユーザーは、性的に露骨なコンテンツ、ヘイトスピーチ、またはコミュニティの基準に違反するその他のコンテンツを報告するためにこのオプションを選択できます。';

  @override
  String get otherCategoryDescription =>
      'その他：上記のカテゴリに簡単に適合しない違反に使用できるキャッチオールカテゴリです。ユーザーが追加の詳細を提供できるように、テキストボックスを含めるのが役立つかもしれません。';

  @override
  String get explainWhatHappens => 'ここに何が起こるかを説明してください';

  @override
  String get loginAgain => '再度ログイン！';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'セッションが終了しました。もう一度ログインしてください。';

  @override
  String get aboutToBlockUserWithConsequences =>
      'このユーザーをブロックしようとしています。彼にチャットを送信したり、グループまたはブロードキャストに追加したりすることはできません！';

  @override
  String
      get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
          'アカウントを削除しようとしています。アカウントはユーザーリストに再表示されません';

  @override
  String get admin => '管理者';

  @override
  String get member => 'メンバー';

  @override
  String get creator => '作成者';

  @override
  String get currentDevice => '現在のデバイス';

  @override
  String get visits => '訪問';

  @override
  String get chooseRoom => 'ルームを選択';

  @override
  String get deleteThisDeviceDesc => 'このデバイスを削除すると、このデバイスからログアウトが即時に実行されます';

  @override
  String get youAreAboutToUpgradeToAdmin => '管理者に昇格しようとしています';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      '現在、ログインは許可されていません。後でもう一度お試しください。';

  @override
  String get dashboard => 'ダッシュボード';

  @override
  String get notification => '通知';

  @override
  String get total => '合計';

  @override
  String get blocked => 'ブロック済み';

  @override
  String get deleted => '削除済み';

  @override
  String get accepted => '承認済み';

  @override
  String get notAccepted => '未承認';

  @override
  String get web => 'ウェブ';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'その他';

  @override
  String get totalVisits => '総訪問数';

  @override
  String get totalMessages => '総メッセージ数';

  @override
  String get textMessages => 'テキストメッセージ';

  @override
  String get imageMessages => '画像メッセージ';

  @override
  String get videoMessages => 'ビデオメッセージ';

  @override
  String get voiceMessages => '音声メッセージ';

  @override
  String get fileMessages => 'ファイルメッセージ';

  @override
  String get infoMessages => '情報メッセージ';

  @override
  String get voiceCallMessages => '音声通話メッセージ';

  @override
  String get videoCallMessages => 'ビデオ通話メッセージ';

  @override
  String get locationMessages => '位置情報メッセージ';

  @override
  String get directChat => 'ダイレクトチャット';

  @override
  String get group => 'グループ';

  @override
  String get broadcast => 'ブロードキャスト';

  @override
  String get messageCounter => 'メッセージカウンター';

  @override
  String get roomCounter => 'ルームカウンター';

  @override
  String get countries => '国';

  @override
  String get devices => 'デバイス';

  @override
  String get notificationTitle => '通知タイトル';

  @override
  String get notificationDescription => '通知の説明';

  @override
  String get notificationsPage => '通知ページ';

  @override
  String get updateFeedBackEmail => 'フィードバックメールを更新';

  @override
  String get setMaxMessageForwardAndShare => '最大メッセージ転送および共有を設定';

  @override
  String get setNewPrivacyPolicyUrl => '新しいプライバシーポリシーのURLを設定';

  @override
  String get forgetPasswordExpireTime => 'パスワードリセットの有効期限';

  @override
  String get callTimeoutInSeconds => '通話タイムアウト（秒）';

  @override
  String get setMaxGroupMembers => '最大グループメンバー数を設定';

  @override
  String get setMaxBroadcastMembers => '最大ブロードキャストメンバー数を設定';

  @override
  String get allowCalls => '通話を許可';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'このオプションが有効になっている場合、ビデオ通話および音声通話が許可されます';

  @override
  String get allowAds => '広告を許可';

  @override
  String get allowMobileLogin => 'モバイルログインを許可';

  @override
  String get allowWebLogin => 'Webログインを許可';

  @override
  String get messages => 'メッセージ';

  @override
  String get appleStoreAppUrl => 'Apple StoreアプリURL';

  @override
  String get googlePlayAppUrl => 'Google PlayアプリURL';

  @override
  String get privacyUrl => 'プライバシーURL';

  @override
  String get feedBackEmail => 'フィードバックメール';

  @override
  String
      get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
          'このオプションが無効になっている場合、チャットファイル、画像、ビデオ、および位置情報の送信がブロックされます';

  @override
  String get allowSendMedia => 'メディアの送信を許可';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'このオプションが無効になっている場合、チャットブロードキャストの作成がブロックされます';

  @override
  String get allowCreateBroadcast => 'ブロードキャストの作成を許可';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'このオプションが無効になっている場合、チャットグループの作成がブロックされます';

  @override
  String get allowCreateGroups => 'グループの作成を許可';

  @override
  String
      get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
          'このオプションが無効になっている場合、デスクトップログインまたは登録（WindowsおよびmacOS）がブロックされます';

  @override
  String get allowDesktopLogin => 'デスクトップログインを許可';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'このオプションが無効になっている場合、Webログインまたは登録がブロックされます';

  @override
  String
      get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
          'このオプションが有効になっている場合、Google広告バナーがチャットに表示されます';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'ユーザープロファイル';

  @override
  String get userInfo => 'ユーザー情報';

  @override
  String get fullName => 'フルネーム';

  @override
  String get bio => 'バイオ';

  @override
  String get noBio => 'バイオなし';

  @override
  String get verifiedAt => '確認済み';

  @override
  String get country => '国';

  @override
  String get registerStatus => '登録ステータス';

  @override
  String get registerMethod => '登録方法';

  @override
  String get banTo => 'ブロック解除日時';

  @override
  String get deletedAt => '削除日時';

  @override
  String get createdAt => '作成日時';

  @override
  String get updatedAt => '更新日時';

  @override
  String get reports => 'レポート';

  @override
  String get clickToSeeAllUserDevicesDetails => 'すべてのユーザーデバイスの詳細を表示するにはクリック';

  @override
  String get allDeletedMessages => 'すべての削除済みメッセージ';

  @override
  String get voiceCallMessage => '音声通話メッセージ';

  @override
  String get totalRooms => '合計ルーム数';

  @override
  String get directRooms => 'ダイレクトルーム';

  @override
  String get userAction => 'ユーザーアクション';

  @override
  String get status => 'ステータス';

  @override
  String get joinedAt => '参加日時';

  @override
  String get saveLogin => 'ログイン情報を保存';

  @override
  String get passwordIsRequired => 'パスワードは必須です';

  @override
  String get verified => '確認済み';

  @override
  String get pending => '保留中';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => '説明は必須です';

  @override
  String get seconds => '秒';

  @override
  String get clickToSeeAllUserInformations => 'すべてのユーザー情報を表示するにはクリック';

  @override
  String get clickToSeeAllUserCountries => 'すべてのユーザー国を表示するにはクリック';

  @override
  String get clickToSeeAllUserMessagesDetails => 'すべてのユーザーメッセージの詳細を表示するにはクリック';

  @override
  String get clickToSeeAllUserRoomsDetails => 'すべてのユーザールームの詳細を表示するにはクリック';

  @override
  String get clickToSeeAllUserReports => 'すべてのユーザーレポートを表示するにはクリック';

  @override
  String get banAt => 'ブロック日時';

  @override
  String
      get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
          '現在、読み取り専用の管理者としてログインしています。これはテストバージョンのため、行ったすべての編集が適用されません。';

  @override
  String get createStory => 'ストーリーを作成';

  @override
  String get writeACaption => 'キャプションを入力...';

  @override
  String get storyCreatedSuccessfully => 'ストーリーの作成に成功しました';

  @override
  String get stories => 'ストーリー';

  @override
  String get clear => 'クリア';

  @override
  String get clearCallsConfirm => '電話をクリアすることを確認しますか？';

  @override
  String get chooseHowAutomaticDownloadWorks => '自動ダウンロードの動作方法を選択';

  @override
  String get whenUsingMobileData => 'モバイルデータを使用している場合';

  @override
  String get whenUsingWifi => 'Wi-Fiを使用している場合';

  @override
  String get image => '画像';

  @override
  String get myPrivacy => 'プライバシー';

  @override
  String get createTextStory => 'テキストストーリーを作成';

  @override
  String get createMediaStory => 'メディアストーリーを作成';

  @override
  String get camera => 'カメラ';

  @override
  String get gallery => 'ギャラリー';

  @override
  String get recentUpdate => '最近の更新';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => '新しいストーリーを追加';

  @override
  String get updateYourProfile => 'プロフィールを更新';

  @override
  String get configureYourAccountPrivacy => 'アカウントのプライバシーを設定';

  @override
  String get youInPublicSearch => 'パブリック検索での表示';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'あなたのプロフィールは、パブリック検索とグループへの追加に表示されます';

  @override
  String get yourLastSeen => '最後に見た';

  @override
  String get yourLastSeenInChats => 'チャットで最後に見た';

  @override
  String get startNewChatWithYou => 'あなたと新しいチャットを開始';

  @override
  String get yourStory => 'あなたのストーリー';

  @override
  String get forRequest => 'リクエスト用';

  @override
  String get public => '公開';

  @override
  String get createYourStory => 'あなたのストーリーを作成';

  @override
  String get shareYourStatus => 'ステータスを共有';

  @override
  String get oneSeenMessage => '一度見たメッセージ';

  @override
  String get messageHasBeenViewed => 'メッセージは閲覧されました';

  @override
  String get clickToSee => '見るにはクリック';

  @override
  String get images => '画像';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String areYouSureRemoveAccount(Object name) {
    return 'Are you sure you want to remove the account for $name?';
  }

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';
}
