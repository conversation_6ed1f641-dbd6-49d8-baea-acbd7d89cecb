// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get done => '완료';

  @override
  String get loading => '로드 중...';

  @override
  String get messageHasBeenDeleted => '메시지가 삭제되었습니다';

  @override
  String get mute => '음소거';

  @override
  String get cancel => '취소';

  @override
  String get typing => '입력 중...';

  @override
  String get ok => '확인';

  @override
  String get recording => '녹음 중...';

  @override
  String get connecting => '연결 중...';

  @override
  String get deleteYouCopy => '내 사본 삭제';

  @override
  String get unMute => '음소거 해제';

  @override
  String get delete => '삭제';

  @override
  String get report => '신고';

  @override
  String get leaveGroup => '그룹 나가기';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      '내 사본을 허용하시겠습니까? 이 작업은 되돌릴 수 없습니다';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      '이 그룹을 나가시겠습니까? 이 작업은 되돌릴 수 없습니다';

  @override
  String get leaveGroupAndDeleteYourMessageCopy => '그룹 나가기 및 내 메시지 사본 삭제';

  @override
  String get vMessageInfoTrans => '메시지 정보';

  @override
  String get updateTitleTo => '제목 업데이트';

  @override
  String get updateImage => '이미지 업데이트';

  @override
  String get joinedBy => '가입한 사람:';

  @override
  String get promotedToAdminBy => '관리자로 승격한 사람:';

  @override
  String get dismissedToMemberBy => '멤버로 내린 사람:';

  @override
  String get leftTheGroup => '그룹에서 나감';

  @override
  String get you => '당신';

  @override
  String get kickedBy => '강퇴한 사람:';

  @override
  String get groupCreatedBy => '그룹 생성자:';

  @override
  String get addedYouToNewBroadcast => '새로운 방송에 당신을 추가했습니다';

  @override
  String get download => '다운로드';

  @override
  String get copy => '복사';

  @override
  String get info => '정보';

  @override
  String get share => '공유';

  @override
  String get forward => '전달';

  @override
  String get reply => '답장';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => '모두에서 삭제';

  @override
  String get deleteFromMe => '나에서 삭제';

  @override
  String get downloading => '다운로드 중...';

  @override
  String get fileHasBeenSavedTo => '파일이 다음 위치에 저장되었습니다';

  @override
  String get online => '온라인';

  @override
  String get youDontHaveAccess => '접근 권한이 없습니다';

  @override
  String get replyToYourSelf => '자기 자신에게 답장';

  @override
  String get repliedToYourSelf => '자기 자신에게 답장함';

  @override
  String get audioCall => '음성 통화';

  @override
  String get ring => '벨 울림';

  @override
  String get canceled => '취소됨';

  @override
  String get timeout => '시간 초과';

  @override
  String get rejected => '거부됨';

  @override
  String get finished => '완료됨';

  @override
  String get inCall => '통화 중';

  @override
  String get sessionEnd => '세션 종료';

  @override
  String get yesterday => '어제';

  @override
  String get today => '오늘';

  @override
  String get textFieldHint => '메시지 입력...';

  @override
  String get files => '파일';

  @override
  String get location => '위치';

  @override
  String get shareMediaAndLocation => '미디어 및 위치 공유';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize => '허용된 크기보다 큰 비디오가 있습니다';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize => '허용된 크기보다 큰 파일이 있습니다';

  @override
  String get makeCall => '통화 시작';

  @override
  String get areYouWantToMakeVideoCall => '비디오 통화를 시작하시겠습니까?';

  @override
  String get areYouWantToMakeVoiceCall => '음성 통화를 시작하시겠습니까?';

  @override
  String get vMessagesInfoTrans => '메시지 정보';

  @override
  String get star => '별표';

  @override
  String get minutes => '분';

  @override
  String get sendMessage => '메시지 보내기';

  @override
  String get deleteUser => '사용자 삭제';

  @override
  String get actions => '작업';

  @override
  String get youAreAboutToDeleteThisUserFromYourList => '이 사용자를 목록에서 삭제하려고 합니다';

  @override
  String get updateBroadcastTitle => '방송 제목 업데이트';

  @override
  String get usersAddedSuccessfully => '사용자가 성공적으로 추가되었습니다';

  @override
  String get broadcastSettings => '방송 설정';

  @override
  String get addParticipants => '참가자 추가';

  @override
  String get broadcastParticipants => '방송 참가자';

  @override
  String get updateGroupDescription => '그룹 설명 업데이트';

  @override
  String get updateGroupTitle => '그룹 제목 업데이트';

  @override
  String get groupSettings => '그룹 설정';

  @override
  String get description => '설명';

  @override
  String get muteNotifications => '알림 음소거';

  @override
  String get groupParticipants => '그룹 참가자';

  @override
  String get blockUser => '사용자 차단';

  @override
  String get areYouSureToBlock => '차단하시겠습니까';

  @override
  String get userPage => '사용자 페이지';

  @override
  String get starMessage => '메시지에 별표';

  @override
  String get showMedia => '미디어 표시';

  @override
  String get reportUser => '사용자 신고';

  @override
  String get groupName => '그룹 이름';

  @override
  String get changeSubject => '주제 변경';

  @override
  String get titleIsRequired => '제목은 필수 항목입니다';

  @override
  String get createBroadcast => '방송 만들기';

  @override
  String get broadcastName => '방송 이름';

  @override
  String get createGroup => '그룹 만들기';

  @override
  String get forgetPassword => '비밀번호 잊어버림';

  @override
  String get globalSearch => '글로벌 검색';

  @override
  String get dismissesToMember => '멤버로 내림';

  @override
  String get setToAdmin => '관리자로 설정';

  @override
  String get kickMember => '멤버 퇴출';

  @override
  String get youAreAboutToDismissesToMember => '멤버를 내리려고 합니다';

  @override
  String get youAreAboutToKick => '멤버를 퇴출하려고 합니다';

  @override
  String get groupMembers => '그룹 멤버';

  @override
  String get tapForPhoto => '사진을 보려면 탭하세요';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      '이 업데이트를 다운로드하는 것을 강력히 권장합니다';

  @override
  String get newGroup => '새 그룹';

  @override
  String get newBroadcast => '새로운 방송';

  @override
  String get starredMessage => '별표 표시된 메시지';

  @override
  String get settings => '설정';

  @override
  String get chats => '채팅';

  @override
  String get recentUpdates => '최근 업데이트';

  @override
  String get startChat => '채팅 시작';

  @override
  String get newUpdateIsAvailable => '새 업데이트가 있습니다';

  @override
  String get emailNotValid => '유효하지 않은 이메일';

  @override
  String get passwordMustHaveValue => '비밀번호는 값을 가져야 합니다';

  @override
  String get error => '오류';

  @override
  String get password => '비밀번호';

  @override
  String get login => '로그인';

  @override
  String get needNewAccount => '새 계정이 필요합니까?';

  @override
  String get register => '등록';

  @override
  String get nameMustHaveValue => '이름은 값을 가져야 합니다';

  @override
  String get passwordNotMatch => '비밀번호가 일치하지 않습니다';

  @override
  String get name => '이름';

  @override
  String get email => '이메일';

  @override
  String get confirmPassword => '비밀번호 확인';

  @override
  String get alreadyHaveAnAccount => '이미 계정이 있으십니까?';

  @override
  String get logOut => '로그아웃';

  @override
  String get back => '뒤로';

  @override
  String get sendCodeToMyEmail => '이메일로 코드 보내기';

  @override
  String get invalidLoginData => '유효하지 않은 로그인 데이터';

  @override
  String get userEmailNotFound => '사용자 이메일을 찾을 수 없습니다';

  @override
  String get yourAccountBlocked => '귀하의 계정이 차단되었습니다';

  @override
  String get yourAccountDeleted => '귀하의 계정이 삭제되었습니다';

  @override
  String get userAlreadyRegister => '사용자가 이미 등록되었습니다';

  @override
  String get codeHasBeenExpired => '코드가 만료되었습니다';

  @override
  String get invalidCode => '유효하지 않은 코드';

  @override
  String get whileAuthCanFindYou => '인증 중에 귀하를 찾을 수 없습니다';

  @override
  String get userRegisterStatusNotAcceptedYet => '사용자 등록 상태가 아직 승인되지 않았습니다';

  @override
  String get deviceHasBeenLogoutFromAllDevices => '모든 기기에서 로그아웃되었습니다';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      '사용자 기기 세션이 종료되었으며 기기가 삭제되었습니다';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      '귀하의 이메일을 확인하기 위한 코드가 전송되지 않았습니다';

  @override
  String get roomAlreadyInCall => '이미 통화 중인 방';

  @override
  String get peerUserInCallNow => '현재 통화 중인 사용자';

  @override
  String get callNotAllowed => '통화가 허용되지 않음';

  @override
  String get peerUserDeviceOffline => '피어 사용자의 기기 오프라인 상태';

  @override
  String get emailMustBeValid => '이메일은 유효해야 합니다';

  @override
  String get wait2MinutesToSendMail => '이메일을 보내려면 2분을 기다려야 합니다';

  @override
  String get codeMustEqualToSixNumbers => '코드는 6자리여야 합니다';

  @override
  String get newPasswordMustHaveValue => '새 비밀번호는 값을 가져야 합니다';

  @override
  String get confirmPasswordMustHaveValue => '비밀번호 확인은 값을 가져야 합니다';

  @override
  String get congregationsYourAccountHasBeenAccepted => '계정이 승인되었습니다';

  @override
  String get yourAccountIsUnderReview => '귀하의 계정은 검토 중입니다';

  @override
  String get waitingList => '대기 목록';

  @override
  String get welcome => '환영합니다';

  @override
  String get retry => '재시도';

  @override
  String get deleteMember => '멤버 삭제';

  @override
  String get profile => '프로필';

  @override
  String get broadcastInfo => '방송 정보';

  @override
  String get updateTitle => '제목 업데이트';

  @override
  String get members => '멤버';

  @override
  String get addMembers => '멤버 추가';

  @override
  String get success => '성공';

  @override
  String get media => '미디어';

  @override
  String get docs => '문서';

  @override
  String get links => '링크';

  @override
  String get soon => '곧';

  @override
  String get unStar => '스타 해제';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      '그룹 설명을 업데이트하면 모든 그룹 멤버가 업데이트됩니다';

  @override
  String get updateNickname => '닉네임 업데이트';

  @override
  String get groupInfo => '그룹 정보';

  @override
  String get youNotParticipantInThisGroup => '이 그룹에 참가자가 아닙니다';

  @override
  String get search => '검색';

  @override
  String get mediaLinksAndDocs => '미디어, 링크 및 문서';

  @override
  String get starredMessages => '스타된 메시지';

  @override
  String get nickname => '닉네임';

  @override
  String get none => '없음';

  @override
  String get yes => '예';

  @override
  String get no => '아니요';

  @override
  String get exitGroup => '그룹 나가기';

  @override
  String get clickToAddGroupDescription => '그룹 설명을 추가하려면 클릭하세요';

  @override
  String get unBlockUser => '사용자 차단 해제';

  @override
  String get areYouSureToUnBlock => '차단 해제하시겠습니까';

  @override
  String get contactInfo => '연락처 정보';

  @override
  String get audio => '오디오';

  @override
  String get video => '비디오';

  @override
  String get hiIamUse => '안녕하세요, 사용 중입니다';

  @override
  String get on => '켜기';

  @override
  String get off => '끄기';

  @override
  String get unBlock => '차단 해제';

  @override
  String get block => '차단';

  @override
  String get chooseAtLestOneMember => '적어도 하나의 멤버를 선택하세요';

  @override
  String get close => '닫기';

  @override
  String get next => '다음';

  @override
  String get appMembers => '앱 멤버';

  @override
  String get create => '생성';

  @override
  String get upgradeToAdmin => '관리자로 업그레이드';

  @override
  String get update => '업데이트';

  @override
  String get deleteChat => '채팅 삭제';

  @override
  String get clearChat => '채팅 지우기';

  @override
  String get showHistory => '기록 보기';

  @override
  String get groupIcon => '그룹 아이콘';

  @override
  String get tapToSelectAnIcon => '아이콘 선택을 위해 탭하세요';

  @override
  String get groupDescription => '그룹 설명';

  @override
  String get more => '더 보기';

  @override
  String get messageInfo => '메시지 정보';

  @override
  String get successfullyDownloadedIn => '다음 위치에 성공적으로 다운로드됨';

  @override
  String get delivered => '전달됨';

  @override
  String get read => '읽음';

  @override
  String get orLoginWith => '또는 다음으로 로그인:';

  @override
  String get resetPassword => '비밀번호 재설정';

  @override
  String get otpCode => 'OTP 코드';

  @override
  String get newPassword => '새 비밀번호';

  @override
  String get areYouSure => '확실합니까?';

  @override
  String get broadcastMembers => '방송 멤버';

  @override
  String get phone => '전화';

  @override
  String get users => '사용자';

  @override
  String get calls => '통화';

  @override
  String get yourAreAboutToLogoutFromThisAccount => '이 계정에서 로그아웃하려고 합니다';

  @override
  String get noUpdatesAvailableNow => '현재 업데이트 없음';

  @override
  String get dataPrivacy => '데이터 개인 정보 보호';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      '모든 데이터가 백업되었으므로 데이터를 직접 관리할 필요가 없습니다. 다시 로그인하면 모든 채팅이 동일하게 표시됩니다.';

  @override
  String get account => '계정';

  @override
  String get linkedDevices => '연결된 기기';

  @override
  String get storageAndData => '저장 및 데이터';

  @override
  String get tellAFriend => '친구에게 알리기';

  @override
  String get help => '도움말';

  @override
  String get blockedUsers => '차단된 사용자';

  @override
  String get inAppAlerts => '앱 내 알림';

  @override
  String get language => '언어';

  @override
  String get adminNotification => '관리자 알림';

  @override
  String get checkForUpdates => '업데이트 확인';

  @override
  String get linkByQrCode => 'QR 코드로 연결';

  @override
  String get deviceStatus => '기기 상태';

  @override
  String get desktopAndOtherDevices => '데스크톱 및 기타 기기';

  @override
  String get linkADeviceSoon => '기기 연결 (곧)';

  @override
  String get lastActiveFrom => '마지막 활동 일시';

  @override
  String get tapADeviceToEditOrLogOut => '편집 또는 로그아웃하려면 기기를 탭하세요.';

  @override
  String get contactUs => '문의하기';

  @override
  String get supportChatSoon => '지원 채팅 (곧)';

  @override
  String get updateYourName => '이름 업데이트';

  @override
  String get updateYourBio => '자기 소개 업데이트';

  @override
  String get edit => '편집';

  @override
  String get about => '정보';

  @override
  String get oldPassword => '이전 비밀번호';

  @override
  String get deleteMyAccount => '내 계정 삭제';

  @override
  String get passwordHasBeenChanged => '비밀번호가 변경되었습니다';

  @override
  String get logoutFromAllDevices => '모든 기기에서 로그아웃하시겠습니까?';

  @override
  String get updateYourPassword => '비밀번호 업데이트';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      '이름을 입력하고 선택적 프로필 사진을 추가하세요';

  @override
  String get privacyPolicy => '개인 정보 보호 정책';

  @override
  String get chat => '채팅';

  @override
  String get send => '보내기';

  @override
  String get reportHasBeenSubmitted => '신고가 제출되었습니다';

  @override
  String get offline => '오프라인';

  @override
  String get harassmentOrBullyingDescription =>
      '괴롭힘 또는 괴롭힘: 이 옵션은 사용자가 자신이나 다른 사람을 괴롭히거나 협박하는 개인을 신고하는 데 사용됩니다.';

  @override
  String get spamOrScamDescription =>
      '스팸 또는 사기: 이 옵션은 스팸 메시지, 무단 광고 메시지를 보내는 계정 또는 다른 사람을 사기치려고 하는 계정을 신고하는 데 사용됩니다.';

  @override
  String get areYouSureToReportUserToAdmin => '이 사용자에 대한 신고를 관리자에게 제출하시겠습니까?';

  @override
  String get groupWith => '그룹과 함께';

  @override
  String get inappropriateContentDescription =>
      '부적절한 콘텐츠: 사용자는 성적으로 음란한 자료, 혐오 발언 또는 공동체 기준을 위반하는 기타 콘텐츠를 신고하기 위해이 옵션을 선택할 수 있습니다.';

  @override
  String get otherCategoryDescription =>
      '기타: 이 옵션은 위의 카테고리에 쉽게 포함되지 않는 위반 사항에 사용할 수 있습니다. 사용자가 추가 세부 정보를 제공할 수 있도록 텍스트 상자를 포함하는 것이 도움이 될 수 있습니다.';

  @override
  String get explainWhatHappens => '여기에 무슨 일이 일어나는지 설명하세요';

  @override
  String get loginAgain => '다시 로그인하세요!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain => '세션이 종료되었습니다. 다시 로그인하세요!';

  @override
  String get aboutToBlockUserWithConsequences =>
      '이 사용자를 차단하려고 합니다. 그에게 채팅을 보낼 수 없으며 그룹이나 방송에 추가할 수 없습니다!';

  @override
  String
      get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
          '계정을 삭제하려고 합니다. 계정은 사용자 목록에 다시 나타나지 않습니다';

  @override
  String get admin => '관리자';

  @override
  String get member => '멤버';

  @override
  String get creator => '창조자';

  @override
  String get currentDevice => '현재 기기';

  @override
  String get visits => '방문';

  @override
  String get chooseRoom => '방 선택';

  @override
  String get deleteThisDeviceDesc => '이 기기를 삭제하면 즉시 로그아웃됩니다';

  @override
  String get youAreAboutToUpgradeToAdmin => '관리자로 업그레이드하려고 합니다';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      '현재 로그인이 허용되지 않았습니다. 나중에 다시 시도하세요.';

  @override
  String get dashboard => '대시보드';

  @override
  String get notification => '알림';

  @override
  String get total => '총';

  @override
  String get blocked => '차단됨';

  @override
  String get deleted => '삭제됨';

  @override
  String get accepted => '수락됨';

  @override
  String get notAccepted => '미수락됨';

  @override
  String get web => '웹';

  @override
  String get android => '안드로이드';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => '기타';

  @override
  String get totalVisits => '총 방문수';

  @override
  String get totalMessages => '총 메시지 수';

  @override
  String get textMessages => '텍스트 메시지';

  @override
  String get imageMessages => '이미지 메시지';

  @override
  String get videoMessages => '비디오 메시지';

  @override
  String get voiceMessages => '음성 메시지';

  @override
  String get fileMessages => '파일 메시지';

  @override
  String get infoMessages => '정보 메시지';

  @override
  String get voiceCallMessages => '음성 통화 메시지';

  @override
  String get videoCallMessages => '비디오 통화 메시지';

  @override
  String get locationMessages => '위치 메시지';

  @override
  String get directChat => '직접 채팅';

  @override
  String get group => '그룹';

  @override
  String get broadcast => '방송';

  @override
  String get messageCounter => '메시지 카운터';

  @override
  String get roomCounter => '방 카운터';

  @override
  String get countries => '국가';

  @override
  String get devices => '기기';

  @override
  String get notificationTitle => '알림 제목';

  @override
  String get notificationDescription => '알림 설명';

  @override
  String get notificationsPage => '알림 페이지';

  @override
  String get updateFeedBackEmail => '피드백 이메일 업데이트';

  @override
  String get setMaxMessageForwardAndShare => '최대 메시지 전달 및 공유 설정';

  @override
  String get setNewPrivacyPolicyUrl => '새 개인 정보 보호 정책 URL 설정';

  @override
  String get forgetPasswordExpireTime => '비밀번호 재설정 만료 시간';

  @override
  String get callTimeoutInSeconds => '통화 제한 시간 (초)';

  @override
  String get setMaxGroupMembers => '최대 그룹 멤버 설정';

  @override
  String get setMaxBroadcastMembers => '최대 방송 멤버 설정';

  @override
  String get allowCalls => '통화 허용';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      '이 옵션이 활성화되면 비디오 및 음성 통화가 허용됩니다';

  @override
  String get allowAds => '광고 허용';

  @override
  String get allowMobileLogin => '모바일 로그인 허용';

  @override
  String get allowWebLogin => '웹 로그인 허용';

  @override
  String get messages => '메시지';

  @override
  String get appleStoreAppUrl => '애플 스토어 앱 URL';

  @override
  String get googlePlayAppUrl => '구글 플레이 앱 URL';

  @override
  String get privacyUrl => '개인 정보 보호 URL';

  @override
  String get feedBackEmail => '피드백 이메일';

  @override
  String
      get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
          '이 옵션이 비활성화된 경우 채팅 파일, 이미지, 비디오 및 위치 전송이 차단됩니다';

  @override
  String get allowSendMedia => '미디어 전송 허용';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      '이 옵션이 비활성화된 경우 채팅 방송 생성이 차단됩니다';

  @override
  String get allowCreateBroadcast => '방송 생성 허용';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      '이 옵션이 비활성화된 경우 채팅 그룹 생성이 차단됩니다';

  @override
  String get allowCreateGroups => '그룹 생성 허용';

  @override
  String
      get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
          '이 옵션이 비활성화된 경우 데스크톱 로그인 또는 등록 (Windows 및 macOS)이 차단됩니다';

  @override
  String get allowDesktopLogin => '데스크톱 로그인 허용';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      '이 옵션이 비활성화된 경우 웹 로그인 또는 등록이 차단됩니다';

  @override
  String
      get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
          '이 옵션이 활성화된 경우 Google 광고 배너가 채팅에 표시됩니다';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => '사용자 프로필';

  @override
  String get userInfo => '사용자 정보';

  @override
  String get fullName => '전체 이름';

  @override
  String get bio => '자기 소개';

  @override
  String get noBio => '자기 소개 없음';

  @override
  String get verifiedAt => '인증됨';

  @override
  String get country => '국가';

  @override
  String get registerStatus => '등록 상태';

  @override
  String get registerMethod => '등록 방법';

  @override
  String get banTo => '차단 종료 시간';

  @override
  String get deletedAt => '삭제 시간';

  @override
  String get createdAt => '생성 시간';

  @override
  String get updatedAt => '업데이트 시간';

  @override
  String get reports => '리포트';

  @override
  String get clickToSeeAllUserDevicesDetails => '모든 사용자 디바이스 세부 정보 보기';

  @override
  String get allDeletedMessages => '모든 삭제된 메시지';

  @override
  String get voiceCallMessage => '음성 통화 메시지';

  @override
  String get totalRooms => '총 방 수';

  @override
  String get directRooms => '직접 방';

  @override
  String get userAction => '사용자 작업';

  @override
  String get status => '상태';

  @override
  String get joinedAt => '가입 일자';

  @override
  String get saveLogin => '로그인 정보 저장';

  @override
  String get passwordIsRequired => '비밀번호 필수';

  @override
  String get verified => '인증됨';

  @override
  String get pending => '보류 중';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => '설명 필수';

  @override
  String get seconds => '초';

  @override
  String get clickToSeeAllUserInformations => '모든 사용자 정보 보기를 클릭하세요';

  @override
  String get clickToSeeAllUserCountries => '모든 사용자 국가 보기를 클릭하세요';

  @override
  String get clickToSeeAllUserMessagesDetails => '모든 사용자 메시지 세부 정보 보기를 클릭하세요';

  @override
  String get clickToSeeAllUserRoomsDetails => '모든 사용자 방 세부 정보 보기를 클릭하세요';

  @override
  String get clickToSeeAllUserReports => '모든 사용자 리포트 보기를 클릭하세요';

  @override
  String get banAt => '차단 일자';

  @override
  String
      get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
          '현재 읽기 전용 관리자로 로그인되었습니다. 이것은 테스트 버전이므로 수행한 모든 편집은 적용되지 않습니다.';

  @override
  String get createStory => '스토리 만들기';

  @override
  String get writeACaption => '캡션 쓰기...';

  @override
  String get storyCreatedSuccessfully => '스토리가 성공적으로 만들어졌습니다';

  @override
  String get stories => '스토리';

  @override
  String get clear => '지우기';

  @override
  String get clearCallsConfirm => '전화 지우기를 확인하시겠습니까?';

  @override
  String get chooseHowAutomaticDownloadWorks => '자동 다운로드 작동 방식 선택';

  @override
  String get whenUsingMobileData => '모바일 데이터 사용 시';

  @override
  String get whenUsingWifi => 'Wi-Fi 사용 시';

  @override
  String get image => '이미지';

  @override
  String get myPrivacy => '개인 정보';

  @override
  String get createTextStory => '텍스트 스토리 만들기';

  @override
  String get createMediaStory => '미디어 스토리 만들기';

  @override
  String get camera => '카메라';

  @override
  String get gallery => '갤러리';

  @override
  String get recentUpdate => '최근 업데이트';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => '새로운 스토리 추가';

  @override
  String get updateYourProfile => '프로필 업데이트';

  @override
  String get configureYourAccountPrivacy => '계정 개인 정보 설정';

  @override
  String get youInPublicSearch => '공개 검색에서 당신';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      '귀하의 프로필은 공개 검색 및 그룹 추가에 표시됩니다';

  @override
  String get yourLastSeen => '마지막으로 본 시간';

  @override
  String get yourLastSeenInChats => '채팅에서 마지막으로 본 시간';

  @override
  String get startNewChatWithYou => '당신과 새로운 채팅 시작';

  @override
  String get yourStory => '당신의 스토리';

  @override
  String get forRequest => '요청 용';

  @override
  String get public => '공개';

  @override
  String get createYourStory => '당신의 스토리 만들기';

  @override
  String get shareYourStatus => '상태 공유';

  @override
  String get oneSeenMessage => '한 번 본 메시지';

  @override
  String get messageHasBeenViewed => '메시지가 확인되었습니다';

  @override
  String get clickToSee => '보려면 클릭';

  @override
  String get images => '이미지';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String areYouSureRemoveAccount(Object name) {
    return 'Are you sure you want to remove the account for $name?';
  }

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';
}
