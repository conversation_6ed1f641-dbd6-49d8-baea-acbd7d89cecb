// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get done => 'Feito';

  @override
  String get loading => 'Carregando ...';

  @override
  String get messageHasBeenDeleted => 'A mensagem foi excluída';

  @override
  String get mute => 'Mudo';

  @override
  String get cancel => 'Cancelar';

  @override
  String get typing => 'Digitando...';

  @override
  String get ok => 'OK';

  @override
  String get recording => 'Gravação...';

  @override
  String get connecting => 'Conectando ...';

  @override
  String get deleteYouCopy => 'Exclua sua cópia';

  @override
  String get unMute => 'Ativação de atividades';

  @override
  String get delete => 'Excluir';

  @override
  String get report => 'Relatório';

  @override
  String get leaveGroup => 'Grupo de licença';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'Você tem certeza de permitir sua cópia?Esta ação não pode desfazer';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'Você tem certeza de deixar este grupo?Esta ação não pode desfazer';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Deixe o grupo e exclua sua cópia de mensagem';

  @override
  String get vMessageInfoTrans => 'Informações da mensagem';

  @override
  String get updateTitleTo => 'Atualizar título para';

  @override
  String get updateImage => 'Atualizar imagem';

  @override
  String get joinedBy => 'Juntado por';

  @override
  String get promotedToAdminBy => 'Promovido a admin por';

  @override
  String get dismissedToMemberBy => 'Demitido ao membro por';

  @override
  String get leftTheGroup => 'Deixou o grupo';

  @override
  String get you => 'Você';

  @override
  String get kickedBy => 'Chutado por';

  @override
  String get groupCreatedBy => 'Grupo criado por';

  @override
  String get addedYouToNewBroadcast => 'Adicionou você a uma nova transmissão';

  @override
  String get download => 'Download';

  @override
  String get copy => 'Cópia';

  @override
  String get info => 'Informações';

  @override
  String get share => 'Compartilhar';

  @override
  String get forward => 'Avançar';

  @override
  String get reply => 'Responder';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Exclua de todos';

  @override
  String get deleteFromMe => 'Exclua de mim';

  @override
  String get downloading => 'Download ...';

  @override
  String get fileHasBeenSavedTo => 'O arquivo foi salvo para';

  @override
  String get online => 'On-line';

  @override
  String get youDontHaveAccess => 'Você não tem acesso';

  @override
  String get replyToYourSelf => 'Responder a si mesmo';

  @override
  String get repliedToYourSelf => 'Respondeu para si mesmo';

  @override
  String get audioCall => 'Chamada de áudio';

  @override
  String get ring => 'Anel';

  @override
  String get canceled => 'Cancelado';

  @override
  String get timeout => 'Tempo esgotado';

  @override
  String get rejected => 'Rejeitado';

  @override
  String get finished => 'Finalizado';

  @override
  String get inCall => 'Na chamada';

  @override
  String get sessionEnd => 'Final da sessão';

  @override
  String get yesterday => 'Ontem';

  @override
  String get today => 'Hoje';

  @override
  String get textFieldHint => 'Digite uma mensagem ...';

  @override
  String get files => 'Arquivos';

  @override
  String get location => 'Localização';

  @override
  String get shareMediaAndLocation => 'Compartilhar mídia e localização';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Há tamanho de vídeo maior do que o tamanho permitido';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Há arquivo tem tamanho maior do que o tamanho permitido';

  @override
  String get makeCall => 'Faça uma chamada';

  @override
  String get areYouWantToMakeVideoCall => 'Você quer fazer videochamada?';

  @override
  String get areYouWantToMakeVoiceCall => 'Você quer fazer uma chamada de voz?';

  @override
  String get vMessagesInfoTrans => 'Informações das mensagens';

  @override
  String get star => 'Estrela';

  @override
  String get minutes => 'Minutos';

  @override
  String get sendMessage => 'Enviar mensagem';

  @override
  String get deleteUser => 'Excluir usuário';

  @override
  String get actions => 'Ações';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Você está prestes a excluir este usuário da sua lista';

  @override
  String get updateBroadcastTitle => 'Atualizar título de transmissão';

  @override
  String get usersAddedSuccessfully => 'Os usuários adicionaram com sucesso';

  @override
  String get broadcastSettings => 'Configurações de transmissão';

  @override
  String get addParticipants => 'Adicionar participantes';

  @override
  String get broadcastParticipants => 'Participantes da transmissão';

  @override
  String get updateGroupDescription => 'Descrição do grupo de atualização';

  @override
  String get updateGroupTitle => 'Título do grupo de atualização';

  @override
  String get groupSettings => 'Configurações do grupo';

  @override
  String get description => 'Descrição';

  @override
  String get muteNotifications => 'Notificações de mudo';

  @override
  String get groupParticipants => 'Participantes do grupo';

  @override
  String get blockUser => 'Bloquear o usuário';

  @override
  String get areYouSureToBlock => 'Você tem certeza de bloquear';

  @override
  String get userPage => 'Página do usuário';

  @override
  String get starMessage => 'Mensagem de estrela';

  @override
  String get showMedia => 'Mostrar mídia';

  @override
  String get reportUser => 'Relatar usuário';

  @override
  String get groupName => 'Nome do grupo';

  @override
  String get changeSubject => 'Mudança de assunto';

  @override
  String get titleIsRequired => 'O título é necessário';

  @override
  String get createBroadcast => 'Crie transmissão';

  @override
  String get broadcastName => 'Nome da transmissão';

  @override
  String get createGroup => 'Criar grupo';

  @override
  String get forgetPassword => 'Esquecer a senha?';

  @override
  String get globalSearch => 'Pesquisa global';

  @override
  String get dismissesToMember => 'Dispensa o membro';

  @override
  String get setToAdmin => 'Definido como admin';

  @override
  String get kickMember => 'Membro do chute';

  @override
  String get youAreAboutToDismissesToMember =>
      'Você está prestes a demitir para o membro';

  @override
  String get youAreAboutToKick => 'Você está prestes a chutar';

  @override
  String get groupMembers => 'Membros do grupo';

  @override
  String get tapForPhoto => 'Toque para foto';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'Recomendamos o download desta atualização';

  @override
  String get newGroup => 'Novo grupo';

  @override
  String get newBroadcast => 'Nova transmissão';

  @override
  String get starredMessage => 'Mensagem estrelada';

  @override
  String get settings => 'Configurações';

  @override
  String get chats => 'Bate -papos';

  @override
  String get recentUpdates => 'Atualizações recentes';

  @override
  String get startChat => 'Inicie o chat';

  @override
  String get newUpdateIsAvailable => 'Nova atualização está disponível';

  @override
  String get emailNotValid => 'Email não é válido';

  @override
  String get passwordMustHaveValue => 'A senha deve ter valor';

  @override
  String get error => 'Erro';

  @override
  String get password => 'Senha';

  @override
  String get login => 'Conecte-se';

  @override
  String get needNewAccount => 'Precisa de uma nova conta?';

  @override
  String get register => 'Registrar';

  @override
  String get nameMustHaveValue => 'Nome deve ter valor';

  @override
  String get passwordNotMatch => 'Senha não corresponde';

  @override
  String get name => 'Nome';

  @override
  String get email => 'E-mail';

  @override
  String get confirmPassword => 'Confirme sua senha';

  @override
  String get alreadyHaveAnAccount => 'Já tem uma conta?';

  @override
  String get logOut => 'Log Out';

  @override
  String get back => 'Voltar';

  @override
  String get sendCodeToMyEmail => 'Enviar código para meu e -mail';

  @override
  String get invalidLoginData => 'Dados de login inválidos';

  @override
  String get userEmailNotFound => 'Email de usuário não encontrado';

  @override
  String get yourAccountBlocked => 'Sua conta foi banida';

  @override
  String get yourAccountDeleted => 'Sua conta foi excluída';

  @override
  String get userAlreadyRegister => 'Usuário já se registra';

  @override
  String get codeHasBeenExpired => 'Código expirou';

  @override
  String get invalidCode => 'Código inválido';

  @override
  String get whileAuthCanFindYou =>
      'Enquanto a autenticação não pode encontrar você';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'Status do registro do usuário ainda não aceito';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'O dispositivo foi logout de todos os dispositivos';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'Dispositivo de sessão de dispositivo de usuário excluído';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'Nenhum código foi enviado para você para verificar seu e -mail';

  @override
  String get roomAlreadyInCall => 'Quarto já de chamada';

  @override
  String get peerUserInCallNow => 'Usuário Ligue agora';

  @override
  String get callNotAllowed => 'Chamada não permitida';

  @override
  String get peerUserDeviceOffline =>
      'Dispositivo de usuário de pares offline offline';

  @override
  String get emailMustBeValid => 'O email deve ser válido';

  @override
  String get wait2MinutesToSendMail => 'Aguarde 2 minutos para enviar e -mail';

  @override
  String get codeMustEqualToSixNumbers =>
      'O código deve ser igual a seis números';

  @override
  String get newPasswordMustHaveValue => 'Nova senha deve ter valor';

  @override
  String get confirmPasswordMustHaveValue => 'Confirmar a senha deve ter valor';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Congregações que sua conta foi aceita';

  @override
  String get yourAccountIsUnderReview => 'Sua conta está em revisão';

  @override
  String get waitingList => 'Lista de espera';

  @override
  String get welcome => 'Bem-vindo';

  @override
  String get retry => 'Tente novamente';

  @override
  String get deleteMember => 'Excluir membro';

  @override
  String get profile => 'Perfil';

  @override
  String get broadcastInfo => 'Informações de transmissão';

  @override
  String get updateTitle => 'Título de atualização';

  @override
  String get members => 'Membros';

  @override
  String get addMembers => 'Adicionar membros';

  @override
  String get success => 'Sucesso';

  @override
  String get media => 'Mídia';

  @override
  String get docs => 'Documentos';

  @override
  String get links => 'Links';

  @override
  String get soon => 'Breve';

  @override
  String get unStar => 'Star da ONU';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Descrição do grupo de atualização atualizará todos os membros do grupo';

  @override
  String get updateNickname => 'Atualize o apelido';

  @override
  String get groupInfo => 'Informações do grupo';

  @override
  String get youNotParticipantInThisGroup => 'Você não participou deste grupo';

  @override
  String get search => 'Procurar';

  @override
  String get mediaLinksAndDocs => 'Mídia, links e documentos';

  @override
  String get starredMessages => 'Mensagens estreladas';

  @override
  String get nickname => 'Apelido';

  @override
  String get none => 'Nenhum';

  @override
  String get yes => 'Sim';

  @override
  String get no => 'Não';

  @override
  String get exitGroup => 'Grupo de saída';

  @override
  String get clickToAddGroupDescription =>
      'Clique para adicionar descrição do grupo';

  @override
  String get unBlockUser => 'Usuário desbloqueie';

  @override
  String get areYouSureToUnBlock => 'Você tem certeza de desbloquear';

  @override
  String get contactInfo => 'Informações de contato';

  @override
  String get audio => 'Áudio';

  @override
  String get video => 'Vídeo';

  @override
  String get hiIamUse => 'Oi estou usando';

  @override
  String get on => 'Sobre';

  @override
  String get off => 'Off';

  @override
  String get unBlock => 'Bloco da ONU';

  @override
  String get block => 'Bloquear';

  @override
  String get chooseAtLestOneMember => 'Escolha pelo menos um membro';

  @override
  String get close => 'Fechar';

  @override
  String get next => 'Next';

  @override
  String get appMembers => 'Membros do aplicativo';

  @override
  String get create => 'Criar';

  @override
  String get upgradeToAdmin => 'Atualizar para admin';

  @override
  String get update => 'Atualizar';

  @override
  String get deleteChat => 'Exclua bate -papo';

  @override
  String get clearChat => 'Bate -papo claro';

  @override
  String get showHistory => 'Mostrar história';

  @override
  String get groupIcon => 'Ícone do grupo';

  @override
  String get tapToSelectAnIcon => 'Toque para selecionar um ícone';

  @override
  String get groupDescription => 'Descrição do grupo';

  @override
  String get more => 'Mais';

  @override
  String get messageInfo => 'Informações da mensagem';

  @override
  String get successfullyDownloadedIn => 'Baixado com sucesso';

  @override
  String get delivered => 'Entregue';

  @override
  String get read => 'Ler';

  @override
  String get orLoginWith => 'ou login com';

  @override
  String get resetPassword => 'Redefinir senha';

  @override
  String get otpCode => 'Código OTP';

  @override
  String get newPassword => 'Nova Senha';

  @override
  String get areYouSure => 'Tem certeza?';

  @override
  String get broadcastMembers => 'Membros da transmissão';

  @override
  String get phone => 'Telefone';

  @override
  String get users => 'Usuários';

  @override
  String get calls => 'Chamados';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Você está prestes a fazer logout desta conta';

  @override
  String get noUpdatesAvailableNow => 'Sem atualizações disponíveis agora';

  @override
  String get dataPrivacy => 'Privacidade de dados';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'Todos os dados foram backup que você não deseja gerenciar o Salvar os dados por si mesmo!Se você fazer logout e fazer login novamente, verá todos os bate -papos iguais para a versão da web';

  @override
  String get account => 'Conta';

  @override
  String get linkedDevices => 'Dispositivos vinculados';

  @override
  String get storageAndData => 'Armazenamento e dados';

  @override
  String get tellAFriend => 'Conte a um amigo';

  @override
  String get help => 'Ajuda';

  @override
  String get blockedUsers => 'Usuários bloqueados';

  @override
  String get inAppAlerts => 'Em alertas de aplicativos';

  @override
  String get language => 'Linguagem';

  @override
  String get adminNotification => 'Notificação de administrador';

  @override
  String get checkForUpdates => 'Verifique se há atualizações';

  @override
  String get linkByQrCode => 'Link por código QR';

  @override
  String get deviceStatus => 'Status do dispositivo';

  @override
  String get desktopAndOtherDevices => 'Desktop e outros dispositivos';

  @override
  String get linkADeviceSoon => 'Vincular um dispositivo (em breve)';

  @override
  String get lastActiveFrom => 'Último ativo de';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Toque em um dispositivo para editar ou fazer logon.';

  @override
  String get contactUs => 'Contate-nos';

  @override
  String get supportChatSoon => 'Apoie o chat (em breve)';

  @override
  String get updateYourName => 'Atualize seu nome';

  @override
  String get updateYourBio => 'Atualize sua biografia';

  @override
  String get edit => 'Editar';

  @override
  String get about => 'Sobre';

  @override
  String get oldPassword => 'Senha Antiga';

  @override
  String get deleteMyAccount => 'Exclua minha conta';

  @override
  String get passwordHasBeenChanged => 'A senha foi alterada';

  @override
  String get logoutFromAllDevices => 'Logout de todos os dispositivos?';

  @override
  String get updateYourPassword => 'Atualize sua senha';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Digite seu nome e adicione uma imagem de perfil opcional';

  @override
  String get privacyPolicy => 'Política de Privacidade';

  @override
  String get chat => 'Bater papo';

  @override
  String get send => 'Enviar';

  @override
  String get reportHasBeenSubmitted => 'Seu relatório foi enviado';

  @override
  String get offline => 'Offline';

  @override
  String get harassmentOrBullyingDescription =>
      'Assédio ou bullying: Esta opção permite que os usuários relatem indivíduos que estão segmentando eles ou outras pessoas com mensagens, ameaças ou outras formas de bullying.';

  @override
  String get spamOrScamDescription =>
      'Spam ou golpe: esta opção seria para os usuários relatarem contas que estão enviando mensagens de spam, anúncios não solicitados ou estão tentando enganar outras pessoas.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Você tem certeza de enviar um relatório sobre esse usuário para o administrador?';

  @override
  String get groupWith => 'Grupo com';

  @override
  String get inappropriateContentDescription =>
      'Conteúdo inadequado: os usuários podem selecionar esta opção para relatar qualquer material sexualmente explícito, discurso de ódio ou outro conteúdo que viole os padrões da comunidade.';

  @override
  String get otherCategoryDescription =>
      'Outro: essa categoria de captura pode ser usada para violações que não se encaixam facilmente nas categorias acima.Pode ser útil incluir uma caixa de texto para os usuários forneceram detalhes adicionais.';

  @override
  String get explainWhatHappens => 'Explique aqui o que acontece';

  @override
  String get loginAgain => 'Faça login novamente!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Sua sessão terminou, faça o login novamente!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Você está prestes a bloquear esse usuário.Você não pode enviar -lhe bate -papos e não pode adicioná -lo a grupos ou transmissão!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'Você está prestes a excluir sua conta, sua conta não aparecerá novamente na lista de usuários';

  @override
  String get admin => 'Administrador';

  @override
  String get member => 'Membro';

  @override
  String get creator => 'Criador';

  @override
  String get currentDevice => 'Dispositivo atual';

  @override
  String get visits => 'Visitas';

  @override
  String get chooseRoom => 'Escolha o quarto';

  @override
  String get deleteThisDeviceDesc =>
      'Excluindo este dispositivo significa logout instantaneamente este dispositivo';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Você está prestes a atualizar para o administrador';

  @override
  String get microphonePermissionMustBeAccepted =>
      'A permissão do microfone deve ser aceita';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'A permissão de microfone e câmera deve ser aceita';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Login agora permitido.Por favor, tente novamente mais tarde.';

  @override
  String get dashboard => 'Painel';

  @override
  String get notification => 'Notificação';

  @override
  String get total => 'Total';

  @override
  String get blocked => 'Bloqueado';

  @override
  String get deleted => 'Excluído';

  @override
  String get accepted => 'Aceito';

  @override
  String get notAccepted => 'Não aceito';

  @override
  String get web => 'Web';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macos';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Other';

  @override
  String get totalVisits => 'Visitas totais';

  @override
  String get totalMessages => 'Total de mensagens';

  @override
  String get textMessages => 'Mensagens de texto';

  @override
  String get imageMessages => 'Mensagens de imagem';

  @override
  String get videoMessages => 'Mensagens de vídeo';

  @override
  String get voiceMessages => 'Mensagens de voz';

  @override
  String get fileMessages => 'Mensagens de arquivo';

  @override
  String get infoMessages => 'Mensagens de informação';

  @override
  String get voiceCallMessages => 'Mensagens de chamada de voz';

  @override
  String get videoCallMessages => 'Mensagens de chamada de vídeo';

  @override
  String get locationMessages => 'Mensagens de localização';

  @override
  String get directChat => 'Chat direto';

  @override
  String get group => 'Grupo';

  @override
  String get broadcast => 'Transmissão';

  @override
  String get messageCounter => 'Contador de mensagens';

  @override
  String get roomCounter => 'Balcão da sala';

  @override
  String get countries => 'Países';

  @override
  String get devices => 'Devices';

  @override
  String get notificationTitle => 'Título de notificação';

  @override
  String get notificationDescription => 'Descrição da notificação';

  @override
  String get notificationsPage => 'Página de notificações';

  @override
  String get updateFeedBackEmail => 'Atualize o email de feedback';

  @override
  String get setMaxMessageForwardAndShare =>
      'Defina a mensagem Max Avan e compartilhe';

  @override
  String get setNewPrivacyPolicyUrl =>
      'Defina um novo URL da Política de Privacidade';

  @override
  String get forgetPasswordExpireTime =>
      'Esqueça o tempo de expiração da senha';

  @override
  String get callTimeoutInSeconds => 'Timeout de chamada em segundos';

  @override
  String get setMaxGroupMembers => 'Defina os membros do Grupo Max';

  @override
  String get setMaxBroadcastMembers =>
      'Defina os membros da transmissão máxima';

  @override
  String get allowCalls => 'Permitir chamadas';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Se esta opção estiver ativada, a chamada de vídeo e voz será permitida.';

  @override
  String get allowAds => 'Permitir anúncios';

  @override
  String get allowMobileLogin => 'Permitir login móvel';

  @override
  String get allowWebLogin => 'Permitir login na web';

  @override
  String get messages => 'Mensagens';

  @override
  String get appleStoreAppUrl => 'URL do aplicativo da Apple Store';

  @override
  String get googlePlayAppUrl => 'URL do aplicativo do Google Play';

  @override
  String get privacyUrl => 'URL de privacidade';

  @override
  String get feedBackEmail => 'E -mail de feedback';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Se esta opção estiver desativada, o envio de arquivos de bate -papo, imagens, vídeos e localização será bloqueado.';

  @override
  String get allowSendMedia => 'Permitir a mídia de envio';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Se esta opção estiver desativada, a criação de transmissão de bate -papo será bloqueada.';

  @override
  String get allowCreateBroadcast => 'Permitir criar transmissão';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Se esta opção estiver desativada, a criação de grupos de bate -papo será bloqueada.';

  @override
  String get allowCreateGroups => 'Permitir grupos de criação';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Se esta opção estiver desativada, o login ou registro da área de trabalho (Windows, Mac) será bloqueado.';

  @override
  String get allowDesktopLogin => 'Permitir login de mesa';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Se esta opção estiver desativada, o login ou registro da Web será bloqueado.';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'Se esta opção estiver desativada, o login ou registro móvel será bloqueado apenas no Android e iOS.';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'Se esta opção estiver ativada, o banner do Google Ads aparecerá nos bate -papos.';

  @override
  String get userProfile => 'Perfil de usuário';

  @override
  String get userInfo => 'Informações do usuário';

  @override
  String get fullName => 'Nome completo';

  @override
  String get bio => 'Bio';

  @override
  String get noBio => 'Sem biografia';

  @override
  String get verifiedAt => 'Verificado em';

  @override
  String get country => 'País';

  @override
  String get registerStatus => 'Status de registro';

  @override
  String get registerMethod => 'Método de registro';

  @override
  String get banTo => 'Proibir para';

  @override
  String get deletedAt => 'Excluído em';

  @override
  String get createdAt => 'Criado em';

  @override
  String get updatedAt => 'Atualizado em';

  @override
  String get reports => 'Relatórios';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Clique para ver todos os detalhes dos dispositivos de usuário';

  @override
  String get allDeletedMessages => 'Todas as mensagens excluídas';

  @override
  String get voiceCallMessage => 'Mensagem de chamada de voz';

  @override
  String get totalRooms => 'Total de quartos';

  @override
  String get directRooms => 'Salas diretas';

  @override
  String get userAction => 'Ação do usuário';

  @override
  String get status => 'Status';

  @override
  String get joinedAt => 'Juntou -se em';

  @override
  String get saveLogin => 'Salvar login';

  @override
  String get passwordIsRequired => 'A senha é necessária';

  @override
  String get verified => 'Verificado';

  @override
  String get pending => 'Pendente';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Descrição é necessária';

  @override
  String get seconds => 'Seconds';

  @override
  String get clickToSeeAllUserInformations =>
      'Clique para ver todas as informações do usuário';

  @override
  String get clickToSeeAllUserCountries =>
      'Clique para ver todos os países de usuários';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Clique para ver todos os detalhes das mensagens do usuário';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Clique para ver todos os detalhes dos quartos do usuário';

  @override
  String get clickToSeeAllUserReports =>
      'Clique para ver todos os relatórios do usuário';

  @override
  String get banAt => 'Proibição em';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Agora você é login como administrador somente leitura.Todas as edições que você fizer não serão aplicadas devido a esta ser uma versão de teste.';

  @override
  String get createStory => 'Criar história';

  @override
  String get writeACaption => 'Escreva uma legenda ...';

  @override
  String get storyCreatedSuccessfully => 'História criada com sucesso';

  @override
  String get stories => 'Histórias';

  @override
  String get clear => 'Clear';

  @override
  String get clearCallsConfirm => 'Chamadas claras confirmam';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Escolha como funciona o download automático';

  @override
  String get whenUsingMobileData => 'Ao usar dados móveis';

  @override
  String get whenUsingWifi => 'Ao usar Wi-Fi';

  @override
  String get image => 'Imagem';

  @override
  String get myPrivacy => 'Minha privacidade';

  @override
  String get createTextStory => 'Crie história de texto';

  @override
  String get createMediaStory => 'Crie uma história de mídia';

  @override
  String get camera => 'Câmera';

  @override
  String get gallery => 'Galeria';

  @override
  String get recentUpdate => 'Atualização recente';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Adicione nova história';

  @override
  String get updateYourProfile => 'Atualize seu perfil';

  @override
  String get configureYourAccountPrivacy =>
      'Configure a privacidade da sua conta';

  @override
  String get youInPublicSearch => 'Você em pesquisa pública';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Seu perfil aparece na pesquisa pública e adicionando grupos';

  @override
  String get yourLastSeen => 'Seu último visto';

  @override
  String get yourLastSeenInChats => 'Seu último visto em bate -papo';

  @override
  String get startNewChatWithYou => 'Comece o novo bate -papo com você';

  @override
  String get yourStory => 'Sua história';

  @override
  String get forRequest => 'Para solicitação';

  @override
  String get public => 'Public';

  @override
  String get createYourStory => 'Crie sua história';

  @override
  String get shareYourStatus => 'Compartilhe seu status';

  @override
  String get oneSeenMessage => 'Uma mensagem vista';

  @override
  String get messageHasBeenViewed => 'A mensagem foi vista';

  @override
  String get clickToSee => 'Clique para ver';

  @override
  String get images => 'Imagens';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String areYouSureRemoveAccount(Object name) {
    return 'Are you sure you want to remove the account for $name?';
  }

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';
}
